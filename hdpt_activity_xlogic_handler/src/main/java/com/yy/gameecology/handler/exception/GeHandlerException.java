package com.yy.gameecology.handler.exception;

/**
 * <AUTHOR> 2019/9/5
 */
public class GeHandlerException extends RuntimeException{

    private int code;

    public GeHandlerException(int code, String message) {
        super(message);
        this.code = code;
    }

    public GeHandlerException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
