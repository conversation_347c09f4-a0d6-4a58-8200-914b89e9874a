
package com.yy.service;

import com.yy.gameecology.common.utils.StringUtil;

import java.util.Map;

public class SvcSdkCallbackBean {
    private long suid;

    private long uid;

    private long topsid;

    private long subsid;

    private byte[] data;

    private Map<Long, String> ext;

    public SvcSdkCallbackBean(long suid, long uid, long topsid, long subsid, byte[] data,
        Map<Long, String> ext) {
        this.suid = suid;
        this.uid = uid;
        this.topsid = topsid;
        this.subsid = subsid;
        this.data = data;
        this.ext = ext;
    }

    public long getSuid() {
        return suid;
    }

    public long getUid() {
        return uid;
    }

    public long getTopsid() {
        return topsid;
    }

    public long getSubsid() {
        return subsid;
    }

    public byte[] getData() {
        return data;
    }

    public Map<Long, String> getExt() {
        return ext;
    }

    public String toString() {
        String userip = ext == null ? "" : ext.get(UserExtKey.UXK_UIP.ordinal());
        userip = userip == null ? "" : userip;
        return String.format("suid:%s  uid:%s topsid:%s subsid:%s data.size:%s userip:%s", suid, uid, topsid, subsid,
            data.length, userip);
    }

    public String toDetail() {
        String content = data == null ? "" : StringUtil.bytes2Hex(data);
        return toString() + " -> [" + content + "]";
    }
}
