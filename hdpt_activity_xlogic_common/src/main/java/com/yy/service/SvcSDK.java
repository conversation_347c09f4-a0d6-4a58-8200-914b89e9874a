/**
 * <AUTHOR>
 */

package com.yy.service;

import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.yxst.lib.svc.sdk.SvcSdkUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/*
 * NOTE:
 * 		long -> 64 bit unsigned Long
 *
 * 		tid  -> topsid
 * 		sid  -> subsid
 *
 * 		svcsdk log dir: /data/yy/log/svcsdk_d.$(pid).$(starttime)/svcsdk_d.$(pid).$(starttime).log
 * 						-> $(pid) is your java process id
 *                      -> $(starttime) is your java process start time
 */
public class SvcSDK {
	// static { System.loadLibrary("svcsdk"); }

	private static final Logger log = LoggerFactory.getLogger(SvcSDK.class);

	static {
		if (!SysEvHelper.isLocal()) {
			String subpath = "lib";
			String path = getCurrentPath(subpath);
			String libname = path.substring(0, path.indexOf(subpath)) + "libsvcsdk.so";
			libname = libname.replace('/', File.separatorChar);
			libname = libname.replace('\\', File.separatorChar);
			libname = libname.replace("//", File.separator);
			log.info("================ libsvcsdk path:" + libname);
			SvcSdkUtil.init(libname);
			log.info("================ libsvcsdk init done!");
			System.load(libname);
			log.info("================ libsvcsdk load done!");
		}
	}

	/**
	 * 以subpath为参考，获取截止到 subpath 的完整路径（以分隔符号结束），本函数可用来定位工作目录
	 *
	 * @param subpath
	 * @return
	 */
	public static String getCurrentPath(String subpath) {
		String path = null;
		try {
			subpath = subpath == null ? "classes" : subpath.trim();
			subpath = subpath.length() == 0 ? "" : subpath;
			subpath = subpath.replace('/', File.separatorChar);
			subpath = subpath.replace('\\', File.separatorChar);
			subpath += subpath.endsWith(File.separator) ? "" : File.separator;
			subpath = (subpath.startsWith(File.separator) ? "" : File.separator) + subpath;

			path = SvcSDK.class.getProtectionDomain().getCodeSource().getLocation().getPath() + "/";
			path = path.replace('/', File.separatorChar);
			path = path.replace('\\', File.separatorChar);
			path += path.endsWith(File.separator) ? "" : File.separator;

			int inx = path.indexOf(subpath);
			final boolean match = path.endsWith(".class") || inx != -1;
			if (match) {
				path = inx == -1 ? path : path.substring(0, inx);
				path += subpath;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		if (path == null) {
			System.out.println("fail to find path with subpath: " + subpath);
		} else {
			System.out.println("find path is: " + path);
		}
		return path;
	}


	/*以上代码不要覆盖 以上代码不要覆盖 以上代码不要覆盖 以上代码不要覆盖 以上代码不要覆盖 以上代码不要覆盖 以上代码不要覆盖 以上代码不要覆盖*/


	/////////////////////////////////////////////////////////////////
	/// Singleton
	public static final SvcSDK getInstance() {
		return SvcSDKHolder.INSTANCE;
	}
	private static class SvcSDKHolder {
		private static final SvcSDK INSTANCE = new SvcSDK();
	}
	private SvcSDK() {
        //System.out.println("create SvcSDK instance");
    }

	/////////////////////////////////////////////////////////////////
	/// set app callback
	private SvcSdkProxyCallbackIf proxyCb   = null;
	private SvcSdkRouteBusCallbackIf rbusCb = null;
	public void setAppCallback(SvcSdkProxyCallbackIf pCb, SvcSdkRouteBusCallbackIf rCb) {
		this.proxyCb = pCb;
		this.rbusCb  = rCb;
	}
	/////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////
	///	native init interface
	public native void sdkInit(SdkInitData init);
	public native void sdkStart();
	public native void sdkDestory();
	/////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////
	///	native module interface
	// range interface
	public native void sdkSubscribeRange(Pair[] rangeSet);
	public native void sdkUnsubscribeRange(Pair[] rangeSet);
	public native void sdkResetRange(Pair[] rangeSet);

	// server info interface
	public native long sdkGetMyId();
	public native long  sdkGetMyIsp();
	public native long  sdkGetMyGroup();

	// svc proxy interface
	public native boolean sdkSendUidMsg(long suid, long uid, byte[] data, Map<String,String> key2Extstr);
	public native boolean sdkSendUidMsgTid(long suid, long tid, long uid, byte[] data, Map<String,String> key2Extstr);
	public native long	  sdkGetUserCount();
	public native long[]  sdkGetUserIp(long suid);
	public native String[]  sdkGetUserIpV6(long suid);
	public native UserIpInfo sdkGetUserIpAll(long suid);
	public native long[]  sdkGetUserTerminalType(long suid);
	public native UserOnlineCtx[] sdkGetUserOnlineCtx(long suid);

	// svc broadcast interface
	/*
	  enum PriorityType
	  {
		EMERGENCY = 0,
		IMPORTANT,
		NORMAL
	  };
	*/
	public native boolean sdkBcTid(long tid, byte[] data);
	public native boolean sdkBcTidApps(long tid, byte[] data, long priority);
	public native boolean sdkBcTidExuid(long tid, long exceptUid, byte[] data, long priority);
	public native boolean sdkBcSid(long tid, long sid, byte[] data);
	public native boolean sdkBcSidApps(long tid, long sid, byte[] data, long priority);
	public native boolean sdkBcSidExuid(long tid, long sid, long exceptUid, byte[] data, long priority);
	public native boolean sdkBcUsergroup(long opUid, long userGroupType, long userGroupId, byte[] data, long priority);
	public native boolean sdkBcTemplate(long uTemplateId, byte[] data);

	public native boolean sdkBcTidSeq(long tid, long opUid, long uKey, byte[] data, long priority);
	public native boolean sdkBcTidExuidSeq(long tid, long opUid, long uKey, long exceptUid, byte[] data, long priority);
	public native boolean sdkBcSidSeq(long tid, long sid, long opUid, long uKey, byte[] data, long priority);
	public native boolean sdkBcSidExuidSeq(long tid, long sid, long opUid, long exceptUid, long uKey, byte[] data, long priority);
	public native boolean sdkBcUidsSeq(long tid, long opUid, long[] uidset, long uKey, byte[] data, long priority);
	public native boolean sdkBcUsergroupSeq(long opUid, long userGroupType, long userGroupId, long uKey, byte[] data, long priority);
	public native boolean sdkBcUsergroupReliable(long opUid, long userGroupType, long userGroupId, long uKey, byte[] data, long priority);

	// svc unicast interface
	public boolean sdkUcUidMsg(long uid, byte[] data) {
	    return sdkUcUidMsg(uid, data, 0, 1);
	}
    public native boolean sdkUcUidMsg(long uid, byte[] data, long tid, long senderUid);

    public boolean sdkUcUidMsgEx(long uid, byte[] data, long clientAppKey,  long platformType, long clientOsType) {
        return sdkUcUidMsgEx(uid, data, clientAppKey, platformType, clientOsType, 0, 1);
    }
    public native boolean sdkUcUidMsgEx(long uid, byte[] data, long clientAppKey,  long platformType, long clientOsType, long tid, long senderUid);

	public boolean sdkUcReliableMsg(long uid, byte[] data) {
	    return sdkUcReliableMsg(uid, data, 0, 1);
	}
	public native boolean sdkUcReliableMsg(long uid, byte[] data, long tid, long senderUid);

	public boolean sdkUcReliableMsgEx(long uid, byte[] data, long clientAppKey,  long platformType, long clientOsType) {
	    return sdkUcReliableMsgEx(uid, data, clientAppKey, platformType, clientOsType, 0, 1);
	}
	public native boolean sdkUcReliableMsgEx(long uid, byte[] data, long clientAppKey,  long platformType, long clientOsType, long tid, long senderUid);

	// svc route bus interface
	public native boolean sdkRBusDispReq(long toSvcType, long uid, long topsid, byte[] data);
	public native boolean sdkRBusDispRes(long toSvcType, long toSvcId, byte[] data);
	public native boolean sdkRBusDisp(long msgType, long toSvcType, long toSvcId, long uid, long topsid, byte[] data);
	/////////////////////////////////////////////////////////////////

	/////////////////////////////////////////////////////////////////
	/// .so native callbacks "in so thread"
	// app server do not change this, just implements interface
	private static final Class<?> RECV_PROXY_MSG_OBJ_CLASS = RecvProxyMsgObj.class;
	private static final Class<?> ROUTE_BUS_MSG_OBJ_CLASS = RouteBusMsgObj.class;
	public void sdkCallback(Object obj) {
		try {
			if (RECV_PROXY_MSG_OBJ_CLASS.isInstance(obj)) {
				RecvProxyMsgObj msg = (RecvProxyMsgObj) obj;
				this.proxyCb.onRecvProxyMsg(msg.getSuid(), msg.getUid(), msg.getTopsid(), msg.getSubsid(), msg.getData(), msg.getExt());
			} else if (ROUTE_BUS_MSG_OBJ_CLASS.isInstance(obj)) {
				RouteBusMsgObj msg = (RouteBusMsgObj) obj;
				this.rbusCb.onRouteBusMsg(msg.getData(), msg.getMsgType(), msg.getFromSvcType(), msg.getFromSvcId());
			} else {
				System.err.println("ERR: native callback obj type " + obj.getClass().getName() + " is unknow!");
			}
		} catch (Exception ex) {
			System.err.println("ERR: exception " + ex.getMessage() + " catch in callback");
			ex.printStackTrace();
			System.exit(-1);
		}
	}
	/////////////////////////////////////////////////////////////////
}

class RecvProxyMsgObj {
	private long	suid;
	private long  uid;
	private long  topsid;
	private long 	subsid;
	private byte[]	data;
	private HashMap<Long, String> ext;

	RecvProxyMsgObj() {
	}
	RecvProxyMsgObj(long suid, long uid, long topsid, long subsid, byte[] data, HashMap<Long, String> ext) {
		this.suid	= suid;
		this.uid	= uid;
		this.topsid = topsid;
		this.subsid = subsid;
		this.data	= data;
		this.ext	= ext;
	}


	public long getSuid() {
		return suid;
	}
	public void setSuid(long suid) {
		this.suid = suid;
	}
	public long getUid() {
		return uid;
	}
	public void setUid(long uid) {
		this.uid = uid;
	}
	public long getTopsid() {
		return topsid;
	}
	public void setTopsid(long topsid) {
		this.topsid = topsid;
	}
	public long getSubsid() {
		return subsid;
	}
	public void setSubsid(long subsid) {
		this.subsid = subsid;
	}
	public byte[] getData() {
		return data;
	}
	public void setData(byte[] data) {
		this.data = data;
	}
	public HashMap<Long, String> getExt() {
		return ext;
	}
	public void setExt(HashMap<Long, String> ext) {
		this.ext = ext;
	}
}

class RouteBusMsgObj {
	private long  msgType;
	private long 	fromSvcType;
	private long	fromSvcId;
	private byte[]	data;

	RouteBusMsgObj() {
	}
	RouteBusMsgObj(long msgType, long fromSvcType, long fromSvcId, byte[] data) {
		this.msgType		= msgType;
		this.fromSvcType	= fromSvcType;
		this.fromSvcId		= fromSvcId;
		this.data			= data;
	}


	public long getMsgType() {
		return msgType;
	}
	public void setMsgType(long msgType) {
		this.msgType = msgType;
	}
	public long getFromSvcType() {
		return fromSvcType;
	}
	public void setFromSvcType(long fromSvcType) {
		this.fromSvcType = fromSvcType;
	}
	public long getFromSvcId() {
		return fromSvcId;
	}
	public void setFromSvcId(long fromSvcId) {
		this.fromSvcId = fromSvcId;
	}
	public byte[] getData() {
		return data;
	}
	public void setData(byte[] data) {
		this.data = data;
	}
}
