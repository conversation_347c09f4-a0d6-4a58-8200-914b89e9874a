package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * 夏日探险记录表
 *
 * <AUTHOR> Assistant
 * @date 2025-07-02
 */
@Data
@TableColumn(underline = true)
public class Cmpt5159SummerAdventureRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5159_summer_adventure_record";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt5159SummerAdventureRecord> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt5159SummerAdventureRecord result = new Cmpt5159SummerAdventureRecord();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getLong("cmpt_use_inx"));
        result.setUserUid(rs.getLong("user_uid"));
        result.setAnchorUid(rs.getLong("anchor_uid"));
        result.setSeq(rs.getString("seq"));
        result.setDiceCount(rs.getInt("dice_count"));
        result.setStartStep(rs.getInt("start_step"));
        result.setEndStep(rs.getInt("end_step"));
        result.setRewardGrids(rs.getString("reward_grids"));
        result.setRewardInfo(rs.getString("reward_info"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        return result;
    };

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 组件使用索引
     */
    private Long cmptUseInx;

    /**
     * 用户UID
     */
    private Long userUid;

    /**
     * 主播UID
     */
    private Long anchorUid;

    /**
     * 探险序列号
     */
    private String seq;

    /**
     * 消耗骰子数量
     */
    private Integer diceCount;

    /**
     * 起始格子
     */
    private Integer startStep;

    /**
     * 结束格子
     */
    private Integer endStep;

    /**
     * 获得奖励的格子列表(JSON)
     */
    private String rewardGrids;

    /**
     * 奖励详情(JSON)
     */
    private String rewardInfo;

    /**
     * 创建时间
     */
    private Date createTime;
}
