/**
 * BaseBridgeThriftClient.java / 2019年8月30日 下午4:55:04
 * <p>
 * Copyright (c) 2019, YY Inc. All Rights Reserved.
 * <p>
 * 郭立平[<EMAIL>]
 */
package com.yy.gameecology.common.support;

import com.yy.gameecology.common.consts.Const.GE_SOURCE;
import com.yy.gameecology.common.utils.Clock;
import com.yy.thrift.gameecology_bridge.*;
import com.yy.thrift.gameecology_bridge.GameecologyBridgeService.Iface;
import org.slf4j.Logger;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019年8月30日 下午4:55:04
 */
public abstract class BaseBridgeThriftClient {

    protected abstract Iface getProxy();

    /**
     * 传特定日志器，方便识别是走的那个 client
     */
    protected Logger log = null;

    protected int source = 0;

    public BaseBridgeThriftClient(Logger log, int source) {
        this.log = log;
        this.source = source;
    }

    public int getSource() {
        return this.source;
    }

    public boolean isGamebaby() {
        return this.source >= GE_SOURCE.GAMEBABY_SOURCE_MIN && this.source <= GE_SOURCE.GAMEBABY_SOURCE_MAX;
    }

    public boolean isGamebabyCore() {
        return this.source == GE_SOURCE.GAMEBABY_CORE;
    }

    public boolean isGamebabyActivity() {
        return this.source == GE_SOURCE.GAMEBABY_ACTIVITY;
    }

    public boolean isYuezhan() {
        return this.source >= GE_SOURCE.YUEZHAN_SOURCE_MIN && this.source <= GE_SOURCE.YUEZHAN_SOURCE_MAX;
    }

    public boolean isYuezhanCore() {
        return this.source == GE_SOURCE.YUEZHAN_CORE;
    }

    public boolean isYuezhanActivity() {
        return this.source == GE_SOURCE.YUEZHAN_ACTIVITY;
    }

    public boolean isMakefriend() {
        return this.source >= GE_SOURCE.MAKEFRIEND_SOURCE_MIN && this.source <= GE_SOURCE.MAKEFRIEND_SOURCE_MAX;
    }

    public boolean isMakefriendProxy() {
        return this.source == GE_SOURCE.MAKEFRIEND_PROXY;
    }

    public boolean isGameecology() {
        return this.source >= GE_SOURCE.MAKEFRIEND_SOURCE_MIN && this.source <= GE_SOURCE.MAKEFRIEND_SOURCE_MAX;
    }

    public boolean isGameecologyCore() {
        return this.source == GE_SOURCE.GAMEECOLOGY_CORE;
    }

    public boolean isGameecologyActivity() {
        return this.source == GE_SOURCE.GAMEECOLOGY_ACTIVITY;
    }

    public boolean isGameecologyProxy() {
        return this.source == GE_SOURCE.GAMEECOLOGY_PROXY;
    }

    public StringResponse read(StringRequest request) {
        Clock clock = new Clock();
        request.setSha256(GeBridgeHelper.sha256(request));
        String params = GeBridgeHelper.toString(request);
        try {
            StringResponse response = getProxy().read(request);
            //log.info("read done@req:{}, resp:{} {}", params, GeBridgeHelper.toString(response), clock.tag());
            return response;
        } catch (Throwable t) {
            log.error("read exception@req:{}, err:{} {}", params, t, clock.tag(), t);
            return GeBridgeHelper.makeResponse(request.getSeq(), 9999, t.getMessage(), request.getRid(), -1);
        }
    }

    public StringResponse write(StringRequest request) {
        Clock clock = new Clock();
        request.setSha256(GeBridgeHelper.sha256(request));
        String params = GeBridgeHelper.toString(request);
        try {
            StringResponse response = getProxy().write(request);
            log.info("write done@req:{}, resp:{} {}", params, GeBridgeHelper.toString(response), clock.tag());
            return response;
        } catch (Throwable t) {
            log.error("write exception@req:{}, err:{} {}", params, t, clock.tag(), t);
            return GeBridgeHelper.makeResponse(request.getSeq(), 9999, t.getMessage(), request.getRid(), -1);
        }
    }

    public BinaryResponse readBinary(BinaryRequest request) {
        Clock clock = new Clock();
        request.setSha256(GeBridgeHelper.sha256(request));
        String params = GeBridgeHelper.toString(request);
        try {
            BinaryResponse response = getProxy().readBinary(request);
            log.info("readBinary done@req:{}, resp:{} {}", params, GeBridgeHelper.toString(response), clock.tag());
            return response;
        } catch (Throwable t) {
            log.error("readBinary exception@req:{}, err:{} {}", params, t, clock.tag(), t);
            return GeBridgeHelper.makeBinaryResponse(request.getSeq(), 9999, t.getMessage(), request.getRid(), -1);
        }
    }

    public BinaryResponse writeBinary(BinaryRequest request) {
        Clock clock = new Clock();
        request.setSha256(GeBridgeHelper.sha256(request));
        String params = GeBridgeHelper.toString(request);
        try {
            BinaryResponse response = getProxy().writeBinary(request);
            log.info("writeBinary done@req:{}, resp:{} {}", params, GeBridgeHelper.toString(response), clock.tag());
            return response;
        } catch (Throwable t) {
            log.error("writeBinary exception@req:{}, err:{} {}", params, t, clock.tag(), t);
            return GeBridgeHelper.makeBinaryResponse(request.getSeq(), 9999, t.getMessage(), request.getRid(), -1);
        }
    }

    public void forward(long suid, long uid, long sid, long ssid, String userip, Map<String, String> extdat,
                        StringRequest request) {
        Clock clock = new Clock();
        request.setSha256(GeBridgeHelper.sha256(request));
        String params = GeBridgeHelper.toString(suid, uid, sid, ssid, extdat, request);
        try {
            getProxy().forward(suid, uid, sid, ssid, userip, extdat, request);
            log.info("forward done@req:{} {}", params, clock.tag());
        } catch (Throwable t) {
            log.error("forward exception@req:{}, err:{} {}", params, t, clock.tag(), t);
        }
    }

    public void unicastByUid(long uid, long sid, StringRequest request) {
        Clock clock = new Clock();
        request.setSha256(GeBridgeHelper.sha256(request));
        String params = GeBridgeHelper.toStringA(uid, sid, request);
        try {
            getProxy().unicastByUid(uid, sid, request);
            log.info("unicastByUid done@req:{} {}", params, clock.tag());
        } catch (Throwable t) {
            log.error("unicastByUid exception@req:{}, err:{} {}", params, t, clock.tag(), t);
        }
    }

    public void unicastBySuid(long suid, StringRequest request) {
        Clock clock = new Clock();
        request.setSha256(GeBridgeHelper.sha256(request));
        String params = GeBridgeHelper.toString(suid, request);
        try {
            getProxy().unicastBySuid(suid, request);
            log.info("unicastBySuid done@req:{} {}", params, clock.tag());
        } catch (Throwable t) {
            log.error("unicastBySuid exception@req:{}, err:{} {}", params, t, clock.tag(), t);
        }
    }

    public void unicastByUids(List<Long> receiveUids, long sid, StringRequest request) {
        Clock clock = new Clock();
        request.setSha256(GeBridgeHelper.sha256(request));
        String params = GeBridgeHelper.toString(receiveUids, sid, request);
        try {
            getProxy().unicastByUids(receiveUids, sid, request);
            log.info("unicastByUids done@req:{} {}", params, clock.tag());
        } catch (Throwable t) {
            log.error("unicastByUids exception@req:{}, err:{} {}", params, t, clock.tag(), t);
        }
    }

    public void unicastBySuids(List<Long> receiveSuids, StringRequest request) {
        Clock clock = new Clock();
        request.setSha256(GeBridgeHelper.sha256(request));
        String params = GeBridgeHelper.toString(receiveSuids, request);
        try {
            getProxy().unicastBySuids(receiveSuids, request);
            log.info("unicastBySuids done@req:{} {}", params, clock.tag());
        } catch (Throwable t) {
            log.error("unicastBySuids exception@req:{}, err:{} {}", params, t, clock.tag(), t);
        }
    }

    public void broadcast2SubChannel(long sid, long ssid, StringRequest request) {
        Clock clock = new Clock();
        request.setSha256(GeBridgeHelper.sha256(request));
        String params = GeBridgeHelper.toStringB(sid, ssid, request);
        try {
            getProxy().broadcast2SubChannel(sid, ssid, request);
            log.info("broadcast2SubChannel done@req:{} {}", params, clock.tag());
        } catch (Throwable t) {
            log.error("broadcast2SubChannel exception@req:{}, err:{} {}", params, t, clock.tag(), t);
        }
    }

    public void broadcast2TopChannel(long sid, StringRequest request) {
        Clock clock = new Clock();
        request.setSha256(GeBridgeHelper.sha256(request));
        String params = GeBridgeHelper.toString(sid, request);
        try {
            getProxy().broadcast2TopChannel(sid, request);
            log.info("broadcast2TopChannel done@req:{} {}", params, clock.tag());
        } catch (Throwable t) {
            log.error("broadcast2TopChannel exception@req:{}, err:{} {}", params, t, clock.tag(), t);
        }
    }

    public void broadcast2SubChannels(List<TopSubChannel> ssids, StringRequest request) {
        Clock clock = new Clock();
        request.setSha256(GeBridgeHelper.sha256(request));
        String params = GeBridgeHelper.toStringC(ssids, request);
        try {
            getProxy().broadcast2SubChannels(ssids, request);
            log.info("broadcast2SubChannels done@req:{} {}", params, clock.tag());
        } catch (Throwable t) {
            log.error("broadcast2SubChannels exception@req:{}, err:{} {}", params, t, clock.tag(), t);
        }
    }

    public void broadcast2TopChannels(List<Long> sids, StringRequest request) {
        Clock clock = new Clock();
        request.setSha256(GeBridgeHelper.sha256(request));
        String params = GeBridgeHelper.toStringD(sids, request);
        try {
            getProxy().broadcast2TopChannels(sids, request);
            log.info("broadcast2TopChannels done@req:{} {}", params, clock.tag());
        } catch (Throwable t) {
            log.error("broadcast2TopChannels exception@req:{}, err:{} {}", params, t, clock.tag(), t);
        }
    }

    public void broadcast2Template(StringRequest request) {
        Clock clock = new Clock();
        request.setSha256(GeBridgeHelper.sha256(request));
        String params = GeBridgeHelper.toString(request);
        try {
            getProxy().broadcast2Template(request);
            log.info("broadcast2Template done@req:{} {}", params, clock.tag());
        } catch (Throwable t) {
            log.error("broadcast2Template exception@req:{}, err:{} {}", params, t, clock.tag(), t);
        }
    }

    public void broadcast2AllChannels(StringRequest request) {
        Clock clock = new Clock();
        String sha256 = GeBridgeHelper.sha256(request);
        log.info("broadcast2AllChannels with sha256:{}", sha256);
        request.setSha256(sha256);
        String params = GeBridgeHelper.toString(request);
        try {
            getProxy().broadcast2AllChannels(request);
            log.info("broadcast2AllChannels done@req:{} {}", params, clock.tag());
        } catch (Throwable t) {
            log.error("broadcast2AllChannels exception@req:{}, err:{} {}", params, t, clock.tag(), t);
        }
    }
}
