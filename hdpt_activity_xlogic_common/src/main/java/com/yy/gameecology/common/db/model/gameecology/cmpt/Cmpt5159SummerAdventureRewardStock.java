package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * 夏日探险奖励库存表
 *
 * <AUTHOR> Assistant
 * @date 2025-07-02
 */
@Data
@TableColumn(underline = true)
public class Cmpt5159SummerAdventureRewardStock implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5159_summer_adventure_reward_stock";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"act_id", "cmpt_use_inx", "grid_no"};

    public static RowMapper<Cmpt5159SummerAdventureRewardStock> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt5159SummerAdventureRewardStock result = new Cmpt5159SummerAdventureRewardStock();
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getLong("cmpt_use_inx"));
        result.setGridNo(rs.getInt("grid_no"));
        result.setRewardName(rs.getString("reward_name"));
        result.setRewardType(rs.getInt("reward_type"));
        result.setRewardValue(rs.getLong("reward_value"));
        result.setTotalStock(rs.getLong("total_stock"));
        result.setUsedStock(rs.getLong("used_stock"));
        result.setDiceThreshold(rs.getLong("dice_threshold"));
        result.setStockIncrement(rs.getLong("stock_increment"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        result.setUpdateTime(rs.getTimestamp("update_time"));
        return result;
    };

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 组件使用索引
     */
    private Long cmptUseInx;

    /**
     * 格子编号(1-30)
     */
    private Integer gridNo;

    /**
     * 奖励名称
     */
    private String rewardName;

    /**
     * 奖励类型(1-头像框 2-气泡 3-礼物 4-入场秀)
     */
    private Integer rewardType;

    /**
     * 奖励价值(厘)
     */
    private Long rewardValue;

    /**
     * 总库存
     */
    private Long totalStock;

    /**
     * 已使用库存
     */
    private Long usedStock;

    /**
     * 骰子阈值(每累计多少个骰子增加库存)
     */
    private Long diceThreshold;

    /**
     * 库存增量
     */
    private Long stockIncrement;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
