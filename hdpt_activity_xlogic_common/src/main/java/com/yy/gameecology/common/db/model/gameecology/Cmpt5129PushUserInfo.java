package com.yy.gameecology.common.db.model.gameecology;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-03-10 20:55
 **/
@Data
@TableColumn(underline = true)
public class Cmpt5129PushUserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5129_push_user_info_";

    public static String getTableName(long actId) {
        return TABLE_NAME + actId;
    }

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt5129PushUserInfo> ROW_MAPPER = null;

    /**
     * uid
     */
    private Long uid;

    /**
     * last_complete_task_time
     */
    private Date lastCompleteTaskTime;

    /**
     * push_time
     */
    private Date pushTime;

    /**
     * push_amount
     */
    private Integer pushAmount;

    /**
     * 1==关闭推送
     */
    private Integer closePush;

    /**
     * create_time
     */
    private Date createTime;

}
