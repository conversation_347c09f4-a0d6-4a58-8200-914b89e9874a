
package com.yy.gameecology.common.cache.support;

import com.google.common.base.Preconditions;
import com.yy.gameecology.common.cache.Cache;
import com.yy.gameecology.common.cache.CacheManager;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.InitializingBean;

/**
 * Title: Description:
 * <p/>
 * User: xuy Date: 11-12-28 下午12:37
 */
@SuppressWarnings("rawtypes")
public class CacheFactoryBean implements FactoryBean, InitializingBean {

    protected final Log log = LogFactory.getLog(getClass());

    private CacheManager cacheManager;

    private String cacheName;

    private Cache cache;

    public void setCacheManager(CacheManager cacheManager) {
        this.cacheManager = cacheManager;
    }

    public void setCacheName(String cacheName) {
        this.cacheName = cacheName;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("Getting Cache '" + cacheName + "'");
        Preconditions.checkState(cacheManager != null, "cacheManager is not set");
        Preconditions.checkState(cacheName != null, "cacheName is not set");
        cache = cacheManager.getCache(cacheName);
        Preconditions.checkState(cache != null, "cache does not exist: " + cacheName);
    }

    @Override
    public Object getObject() throws Exception {
        return this.cache;
    }

    @Override
    public Class getObjectType() {
        return (this.cache != null ? this.cache.getClass() : Cache.class);
    }

    @Override
    public boolean isSingleton() {
        return true;
    }

}
