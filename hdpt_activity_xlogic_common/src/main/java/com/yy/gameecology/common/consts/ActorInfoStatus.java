package com.yy.gameecology.common.consts;

/**
 * desc:对象状态 -1非参赛角色（没在分组名单） 0-正常状态（在晋级线以上） 1-代表有危险（在晋级线以下） 2-被淘汰
 *
 * @createBy 曾文帜
 * @create 2020-07-27 10:17
 **/
public class ActorInfoStatus {
    /**
     * -1非参赛角色（没在分组名单）
     */
    public static final int NOT_IN = -1;

    /**
     * 0-正常状态（在晋级线以上）
     */
    public static final int NORMAL = 0;

    /**
     * 1-代表有危险（在晋级线以下）
     */

    public static final int DANGEROUS = 1;

    /**
     * 2- 报了名，但是在本阶段不参赛: 被淘  ||  未参加本轮，例如复活赛
     */

    public static final int ELIMINATE = 2;

    /**
     * 报了名，但是没在阶段时间内
     */
    public static final int NOT_IN_PHASE = 4;

    /**
     * 晋级配置没有配置胜利策略
     */
    public static final int NO_VICTORY = -99;


}
