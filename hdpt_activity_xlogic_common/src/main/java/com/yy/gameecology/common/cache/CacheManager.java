package com.yy.gameecology.common.cache;

import java.util.Collection;

/**
 * Entity managing {@link Cache}s.
 * 
 * <AUTHOR> <PERSON>u
 */
public interface CacheManager {

	/**
	 * Returns the cache associated with the given name. 
	 * 
	 * @param name cache identifier - cannot be null
	 * @return associated cache or null if none is found
	 */
	Cache getCache(String name);

	/**
	 * Returns a collection of the caches known by this cache manager. 
	 * 
	 * @return names of caches known by the cache manager. 
	 */
	Collection<String> getCacheNames();
}
