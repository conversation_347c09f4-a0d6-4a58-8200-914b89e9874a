package com.yy.gameecology.common.db.mapper.pepc;

import com.yy.gameecology.common.db.model.gameecology.pepc.PepcAwardRecord;

public interface PepcMatchAwardRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PepcAwardRecord record);

    int insertSelective(PepcAwardRecord record);

    PepcAwardRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PepcAwardRecord record);

    int updateByPrimaryKey(PepcAwardRecord record);
}
