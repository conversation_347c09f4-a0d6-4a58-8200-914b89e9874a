package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * 夏日探险统计数据表
 *
 * <AUTHOR> Assistant
 * @date 2025-07-02
 */
@Data
@TableColumn(underline = true)
public class Cmpt5159SummerAdventureStatistics implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5159_summer_adventure_statistics";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"act_id", "cmpt_use_inx", "stat_date"};

    public static RowMapper<Cmpt5159SummerAdventureStatistics> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt5159SummerAdventureStatistics result = new Cmpt5159SummerAdventureStatistics();
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getLong("cmpt_use_inx"));
        result.setStatDate(rs.getString("stat_date"));
        result.setTotalDiceGenerated(rs.getLong("total_dice_generated"));
        result.setTotalDiceUsed(rs.getLong("total_dice_used"));
        result.setTotalCpParticipated(rs.getLong("total_cp_participated"));
        result.setTodayCpParticipated(rs.getLong("today_cp_participated"));
        result.setTotalRewardAmount(rs.getLong("total_reward_amount"));
        result.setTotalLotteryTimes(rs.getLong("total_lottery_times"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        result.setUpdateTime(rs.getTimestamp("update_time"));
        return result;
    };

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 组件使用索引
     */
    private Long cmptUseInx;

    /**
     * 统计日期(YYYY-MM-DD)
     */
    private String statDate;

    /**
     * 累计产生骰子数
     */
    private Long totalDiceGenerated;

    /**
     * 累计使用骰子数
     */
    private Long totalDiceUsed;

    /**
     * 累计参与CP次数
     */
    private Long totalCpParticipated;

    /**
     * 今日参与CP次数
     */
    private Long todayCpParticipated;

    /**
     * 累计奖励金额(厘)
     */
    private Long totalRewardAmount;

    /**
     * 累计抽奖次数
     */
    private Long totalLotteryTimes;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
