package com.yy.gameecology.common.db.mapper.aov;

import com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseTeam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface AovPhaseTeamMapper {

    int insertAndGetId(AovPhaseTeam record);

    AovPhaseTeam selectById(@Param("id") long id);

    List<AovPhaseTeam> selectByIds(@Param("teamIds") List<Long> teamIds);

    AovPhaseTeam selectAovPhaseTeam(@Param("uid") long uid, @Param("phaseId") long phaseId);

    List<AovPhaseTeam> selectInitTeam(@Param("phaseId") long phaseId, @Param("cnt") long cnt, @Param("state") int state);

    List<AovPhaseTeam> listTeam(@Param("phaseId") long phaseId, @Param("cnt") long cnt);

    List<AovPhaseTeam>  pageList(@Param("phaseId") long phaseId, @Param("offset") int offset, @Param("pageSize") int pageSize);

    int incrTeamCnt(@Param("id") long id, @Param("score") long score);

    int decrTeamCnt(@Param("id") long id, @Param("score") long score);

    int invalidTeam(@Param("phaseId") long phaseId, @Param("cnt") long cnt, @Param("state") int state);

    List<AovPhaseTeam> selectValidTeamIds(@Param("phaseId") long phaseId);

    int updateState(@Param("ids") List<Long> ids, @Param("state") int state);

    int updateDeclaration(@Param("declarationAudit") String declarationAudit, @Param("auditState") int auditState, @Param("id") long id);

    int acceptDeclaration( @Param("auditState") int auditState, @Param("id") long id, @Param("oldState") int oldState);

    int clearAuditDeclaration( @Param("auditState") int auditState, @Param("id") long id, @Param("oldState") int oldState);

    AovPhaseTeam selectPhaseTeamBySid(@Param("phaseId") long phaseId, @Param("sid") long sid, @Param("ssid") long ssid);

    int countTeam(@Param("phaseId") long phaseId, @Param("state") Integer state, @Param("floorMemberCnt") Integer floorMemberCnt);

    int turnNeedApply(@Param("teamId") long teamId);

    int deleteTeam(@Param("teamId") long teamId);

    @Update("""
    update  aov_phase_team set team_name = #{teamName} where id = #{id}
    """)
    int updateTeamName(@Param("id") long id, @Param("teamName") String teamName);

    @Update("""
    update  aov_phase_team set team_name_audit = #{teamNameAudit} where id = #{id}
    """)
    int updateTeamNameAudit(@Param("id") long id, @Param("teamNameAudit") String teamNameAudit);

    @Update("""
    update  aov_phase_team set team_name_audit_state = #{status} where id = #{id}
    """)
    int updateTeamNameAuditStatue(@Param("id") long id, @Param("status") int status);
}
