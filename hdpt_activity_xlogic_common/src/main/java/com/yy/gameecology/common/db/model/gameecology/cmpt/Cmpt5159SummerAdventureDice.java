package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * 夏日探险骰子数据表
 *
 * <AUTHOR> Assistant
 * @date 2025-07-02
 */
@Data
@TableColumn(underline = true)
public class Cmpt5159SummerAdventureDice implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5159_summer_adventure_dice";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt5159SummerAdventureDice> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt5159SummerAdventureDice result = new Cmpt5159SummerAdventureDice();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getLong("cmpt_use_inx"));
        result.setUserUid(rs.getLong("user_uid"));
        result.setAnchorUid(rs.getLong("anchor_uid"));
        result.setTotalCount(rs.getLong("total_count"));
        result.setBalance(rs.getLong("balance"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        result.setUpdateTime(rs.getTimestamp("update_time"));
        return result;
    };

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 组件使用索引
     */
    private Long cmptUseInx;

    /**
     * 用户UID
     */
    private Long userUid;

    /**
     * 主播UID
     */
    private Long anchorUid;

    /**
     * 累计获得骰子总数
     */
    private Long totalCount;

    /**
     * 当前剩余骰子数
     */
    private Long balance;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
