package com.yy.gameecology.activity.service.gensql;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-05-28 10:58
 **/
public class GenBabySqlTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public static void main(String[] args) {

        File fileSource = new File("D:\\tmp\\超凡签到天数初始化.txt");

        String sql = "insert into merchant_alliance.tc_award_user_profile(uid,biz_type, sub_type, create_time, modify_time) " +
                "VALUE( ${uid} ,'lucky_bag_sub', ${dayCode} ,now(),now())  on duplicate key update modify_time=now();";

        try {

            InputStreamReader reader = new InputStreamReader(new FileInputStream(fileSource));
            BufferedReader br = new BufferedReader(reader);
            String line = br.readLine();
            StringBuilder sqlOutPut = new StringBuilder();


            while (line != null) {
                String uid = line.substring(line.indexOf("\"") + 1, line.lastIndexOf("\""));
                for (int i = 1; i <= 15; i++) {
                    String lineSql = sql.replace("${uid}", uid).replace("${dayCode}", "202105" + (i < 10 ? "0" + i : i));
                    sqlOutPut.append(lineSql);
                    sqlOutPut.append("\r\n");
                }


                line = br.readLine();

            }


            //写入文件
            File writename = new File("D:\\tmp\\超凡签到天数初始化_output.txt");
            // 创建新文件
            writename.createNewFile();
            BufferedWriter out = new BufferedWriter(new FileWriter(writename));
            out.write(sqlOutPut.toString());


            reader.close();
            out.flush();
            out.close();


        } catch (Exception e) {

        }

    }
}
