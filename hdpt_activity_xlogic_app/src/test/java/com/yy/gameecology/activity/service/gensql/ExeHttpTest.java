package com.yy.gameecology.activity.service.gensql;

import com.yy.gameecology.common.utils.HttpClientHelper;
import com.yy.gameecology.common.utils.MyCharsets;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.util.List;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-05-14 19:31
 **/
public class ExeHttpTest {
    private final static Logger log = LoggerFactory.getLogger(ExeHttpTest.class);

    public static void main(String[] args) throws Exception {

        List<String> source = FileUtils.readLines(new File("D:\\tmp\\httpExe.txt"), "UTF-8");
        StringBuilder outPut = new StringBuilder();
        HttpClientHelper http = new HttpClientHelper(MyCharsets.UTF_8);
        for (String item : source) {
            try {
                String resultContent = http.excuteGet(item);
                String exeLine = item + "|" + resultContent;
                System.out.println(exeLine);
                outPut.append(exeLine);
                outPut.append("|");
                outPut.append("\r\n");
            } catch (Exception e) {
                log.error("excuteGet error,item:{},e:{}", item, e.getMessage(), e);
            }
        }

        try {
            //写入文件
            File writename = new File("D:\\tmp\\httpExe_out.txt");
            // 创建新文件
            writename.createNewFile();
            BufferedWriter out = new BufferedWriter(new FileWriter(writename));
            out.write(outPut.toString());

            out.flush();
            out.close();


        } catch (Exception e) {
            log.error("write error,e:{}", e.getMessage(), e);
        }


    }
}
