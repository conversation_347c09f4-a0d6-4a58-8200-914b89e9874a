package com.yy.gameecology.activity.client.http;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.turnover.PropsInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2019/12/9
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties"})
public class TurnoverServiceHttpClientTest {

    @Autowired
    private TurnoverServiceHttpClient turnoverServiceHttpClient;


    @Test
    public void queryAllGiftInfoTest() {

        List<PropsInfo> propsInfos = turnoverServiceHttpClient.queryAllGiftInfoList();

        System.out.println(propsInfos);

    }

    @Test
    public void queryAllGiftInfoMapTest() {

        Map<Integer, PropsInfo> propsInfoMap = turnoverServiceHttpClient.queryAllGiftInfoMap();

        System.out.println(propsInfoMap);

    }
}
