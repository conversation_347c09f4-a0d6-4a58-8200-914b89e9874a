package com.yy.gameecology.hdzj.element.component.bean;

import lombok.Data;

import java.util.List;

/**
 * 夏日探险信息
 *
 * <AUTHOR> Assistant
 * @date 2025-07-02
 */
@Data
public class SummerAdventureInfo {

    /**
     * CP信息
     */
    private CpInfo cpInfo;

    /**
     * 骰子信息
     */
    private DiceInfo diceInfo;

    /**
     * 地图信息
     */
    private MapInfo mapInfo;

    /**
     * 奖池信息
     */
    private List<RewardPoolInfo> rewardPools;

    /**
     * 中奖记录
     */
    private RecordInfo recordInfo;

    @Data
    public static class CpInfo {
        /**
         * 用户UID
         */
        private Long userUid;

        /**
         * 用户昵称
         */
        private String userNick;

        /**
         * 用户头像
         */
        private String userAvatar;

        /**
         * 主播UID
         */
        private Long anchorUid;

        /**
         * 主播昵称
         */
        private String anchorNick;

        /**
         * 主播头像
         */
        private String anchorAvatar;

        /**
         * 是否可切换CP
         */
        private Boolean canSwitchCp;

        /**
         * 可选CP列表
         */
        private List<CpOption> cpOptions;
    }

    @Data
    public static class CpOption {
        private Long userUid;
        private String userNick;
        private String userAvatar;
        private Long anchorUid;
        private String anchorNick;
        private String anchorAvatar;
        private Long diceCount;
    }

    @Data
    public static class DiceInfo {
        /**
         * 当前骰子数
         */
        private Long currentCount;

        /**
         * 累计获得骰子数
         */
        private Long totalCount;

        /**
         * 骰子上限
         */
        private Long maxLimit;

        /**
         * 是否达到上限
         */
        private Boolean reachedLimit;

        /**
         * 本次消耗数量提示
         */
        private String consumeHint;

        /**
         * 获取骰子提示
         */
        private String getHint;
    }

    @Data
    public static class MapInfo {
        /**
         * 当前位置
         */
        private Integer currentPosition;

        /**
         * 地图格子总数
         */
        private Integer totalGrids;

        /**
         * 格子信息列表
         */
        private List<GridInfo> grids;
    }

    @Data
    public static class GridInfo {
        /**
         * 格子编号
         */
        private Integer gridNo;

        /**
         * 奖励名称
         */
        private String rewardName;

        /**
         * 奖励图标
         */
        private String rewardIcon;

        /**
         * 奖励类型
         */
        private Integer rewardType;

        /**
         * 是否有库存
         */
        private Boolean hasStock;

        /**
         * 剩余库存
         */
        private Long remainingStock;
    }

    @Data
    public static class RewardPoolInfo {
        /**
         * 格子编号
         */
        private Integer gridNo;

        /**
         * 奖励名称
         */
        private String rewardName;

        /**
         * 总库存
         */
        private Long totalStock;

        /**
         * 剩余库存
         */
        private Long remainingStock;

        /**
         * 累计发放次数
         */
        private Long issuedCount;

        /**
         * 实时概率
         */
        private Double probability;
    }

    @Data
    public static class RecordInfo {
        /**
         * 我的中奖记录
         */
        private List<AdventureRecord> myRecords;

        /**
         * 全服中奖记录
         */
        private List<AdventureRecord> globalRecords;
    }

    @Data
    public static class AdventureRecord {
        /**
         * 用户UID
         */
        private Long userUid;

        /**
         * 用户昵称
         */
        private String userNick;

        /**
         * 用户头像
         */
        private String userAvatar;

        /**
         * 主播UID
         */
        private Long anchorUid;

        /**
         * 主播昵称
         */
        private String anchorNick;

        /**
         * 主播头像
         */
        private String anchorAvatar;

        /**
         * 奖励信息
         */
        private String rewardInfo;

        /**
         * 奖励价值
         */
        private Long rewardValue;

        /**
         * 是否大奖
         */
        private Boolean isBigReward;

        /**
         * 获奖时间
         */
        private Long createTime;

        /**
         * 主播频道信息
         */
        private ChannelInfo channelInfo;
    }

    @Data
    public static class ChannelInfo {
        private Long sid;
        private Long ssid;
        private String channelName;
    }
}
