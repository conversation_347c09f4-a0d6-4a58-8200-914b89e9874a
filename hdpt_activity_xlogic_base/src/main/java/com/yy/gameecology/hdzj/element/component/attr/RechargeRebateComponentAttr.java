package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.RechargeRewardInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2023/8/22
 */
@Data
public class RechargeRebateComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "礼包列表", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = RechargeRewardInfo.class))
    private List<RechargeRewardInfo> rewardInfos;

    @ComponentAttrField(labelText = "黑名单", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class))
    private List<Long> blacklist;

    @ComponentAttrField(labelText = "最小充值金额")
    private long chargeThreshold = 100L;

    @ComponentAttrField(labelText = "奖池id")
    private Long taskId;

}
