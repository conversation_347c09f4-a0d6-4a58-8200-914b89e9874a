package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.component.attr.bean.SummerAdventureGridConfig;
import lombok.Data;

/**
 * 夏日探险组件属性配置
 *
 * <AUTHOR> Assistant
 * @date 2025-07-02
 */
@Data
public class SummerAdventureComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", remark = "200:游戏生态,400:游戏宝贝,500:交友,600:约战,900:陪玩")
    private Long busiId;

    @ComponentAttrField(labelText = "活动礼物ID列表", remark = "多个用逗号分隔，仅限甜甜雪糕、炽恋盛夏、夏日票、粉丝票")
    private String giftIds;

    @ComponentAttrField(labelText = "骰子获取金额阈值", remark = "单位：厘，默认131400(131.4元)")
    private Long diceThreshold = 131400L;

    @ComponentAttrField(labelText = "单个CP骰子上限", remark = "默认500个")
    private Integer maxDiceLimit = 500;

    @ComponentAttrField(labelText = "单次最大消耗骰子数", remark = "默认30个")
    private Integer maxConsumePerTime = 30;

    @ComponentAttrField(labelText = "地图格子总数", remark = "默认30个")
    private Integer totalGrids = 30;

    @ComponentAttrField(labelText = "格子奖励配置")
    private SummerAdventureGridConfig[] gridConfigs;

    @ComponentAttrField(labelText = "广播模板", remark = "200:游戏生态,400:游戏宝贝,500:交友,600:约战,900:陪玩")
    private Integer broadcastTemplate;

    @ComponentAttrField(labelText = "大奖广播阈值", remark = "奖励价值超过此值时全服广播，单位：厘，默认52000(52元)")
    private Long bigRewardThreshold = 52000L;

    @ComponentAttrField(labelText = "黑名单检查开关", remark = "1-开启 0-关闭")
    private Integer blacklistCheck = 1;

    @ComponentAttrField(labelText = "首次获得骰子提示开关", remark = "1-开启 0-关闭")
    private Integer firstDiceNotice = 1;

    @ComponentAttrField(labelText = "生产群推送开关", remark = "1-开启 0-关闭")
    private Integer productionPush = 1;

    @ComponentAttrField(labelText = "统计数据推送间隔", remark = "单位：分钟，默认60分钟")
    private Integer statsPushInterval = 60;
}
