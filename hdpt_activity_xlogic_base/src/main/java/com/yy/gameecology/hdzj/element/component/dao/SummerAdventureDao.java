package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.cmpt.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 夏日探险数据访问层
 *
 * <AUTHOR> Assistant
 * @date 2025-07-02
 */
@Slf4j
@Repository
public class SummerAdventureDao {

    @Autowired
    private GameecologyDao gameecologyDao;

    /**
     * 获取CP的骰子信息
     */
    public Cmpt5159SummerAdventureDice getDiceInfo(long actId, long cmptUseInx, long userUid, long anchorUid) {
        Cmpt5159SummerAdventureDice query = new Cmpt5159SummerAdventureDice();
        query.setActId(actId);
        query.setCmptUseInx(cmptUseInx);
        query.setUserUid(userUid);
        query.setAnchorUid(anchorUid);
        
        List<Cmpt5159SummerAdventureDice> list = gameecologyDao.select(Cmpt5159SummerAdventureDice.class, query);
        if (list.isEmpty()) {
            // 创建新记录
            Cmpt5159SummerAdventureDice newRecord = new Cmpt5159SummerAdventureDice();
            newRecord.setActId(actId);
            newRecord.setCmptUseInx(cmptUseInx);
            newRecord.setUserUid(userUid);
            newRecord.setAnchorUid(anchorUid);
            newRecord.setTotalCount(0L);
            newRecord.setBalance(0L);
            newRecord.setCreateTime(new Date());
            newRecord.setUpdateTime(new Date());
            
            gameecologyDao.insert(newRecord);
            return newRecord;
        }
        return list.get(0);
    }

    /**
     * 更新骰子数量
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDiceCount(long actId, long cmptUseInx, long userUid, long anchorUid, 
                                   long addCount, long consumeCount) {
        String sql = "UPDATE " + Cmpt5159SummerAdventureDice.TABLE_NAME + 
                    " SET total_count = total_count + ?, balance = balance + ? - ?, update_time = NOW()" +
                    " WHERE act_id = ? AND cmpt_use_inx = ? AND user_uid = ? AND anchor_uid = ?";
        
        int rows = gameecologyDao.getJdbcTemplate().update(sql, 
                addCount, addCount, consumeCount, actId, cmptUseInx, userUid, anchorUid);
        
        return rows > 0;
    }

    /**
     * 消耗骰子
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean consumeDice(long actId, long cmptUseInx, long userUid, long anchorUid, long consumeCount) {
        String sql = "UPDATE " + Cmpt5159SummerAdventureDice.TABLE_NAME + 
                    " SET balance = balance - ?, update_time = NOW()" +
                    " WHERE act_id = ? AND cmpt_use_inx = ? AND user_uid = ? AND anchor_uid = ? AND balance >= ?";
        
        int rows = gameecologyDao.getJdbcTemplate().update(sql, 
                consumeCount, actId, cmptUseInx, userUid, anchorUid, consumeCount);
        
        return rows > 0;
    }

    /**
     * 插入探险记录
     */
    public void insertAdventureRecord(Cmpt5159SummerAdventureRecord record) {
        gameecologyDao.insert(record);
    }

    /**
     * 获取用户的探险记录
     */
    public List<Cmpt5159SummerAdventureRecord> getUserRecords(long actId, long cmptUseInx, 
                                                              long userUid, long anchorUid, 
                                                              int limit) {
        String sql = "SELECT * FROM " + Cmpt5159SummerAdventureRecord.TABLE_NAME + 
                    " WHERE act_id = ? AND cmpt_use_inx = ? AND user_uid = ? AND anchor_uid = ?" +
                    " ORDER BY create_time DESC LIMIT ?";
        
        return gameecologyDao.getJdbcTemplate().query(sql, 
                Cmpt5159SummerAdventureRecord.ROW_MAPPER,
                actId, cmptUseInx, userUid, anchorUid, limit);
    }

    /**
     * 获取全服最新中奖记录
     */
    public List<Cmpt5159SummerAdventureRecord> getGlobalRecords(long actId, long cmptUseInx, int limit) {
        String sql = "SELECT * FROM " + Cmpt5159SummerAdventureRecord.TABLE_NAME + 
                    " WHERE act_id = ? AND cmpt_use_inx = ?" +
                    " ORDER BY create_time DESC LIMIT ?";
        
        return gameecologyDao.getJdbcTemplate().query(sql, 
                Cmpt5159SummerAdventureRecord.ROW_MAPPER,
                actId, cmptUseInx, limit);
    }

    /**
     * 获取奖励库存信息
     */
    public List<Cmpt5159SummerAdventureRewardStock> getRewardStocks(long actId, long cmptUseInx) {
        Cmpt5159SummerAdventureRewardStock query = new Cmpt5159SummerAdventureRewardStock();
        query.setActId(actId);
        query.setCmptUseInx(cmptUseInx);
        
        return gameecologyDao.select(Cmpt5159SummerAdventureRewardStock.class, query);
    }

    /**
     * 更新奖励库存
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRewardStock(long actId, long cmptUseInx, int gridNo, long useCount) {
        String sql = "UPDATE " + Cmpt5159SummerAdventureRewardStock.TABLE_NAME + 
                    " SET used_stock = used_stock + ?, update_time = NOW()" +
                    " WHERE act_id = ? AND cmpt_use_inx = ? AND grid_no = ? AND (total_stock = -1 OR used_stock + ? <= total_stock)";
        
        int rows = gameecologyDao.getJdbcTemplate().update(sql, 
                useCount, actId, cmptUseInx, gridNo, useCount);
        
        return rows > 0;
    }

    /**
     * 初始化奖励库存
     */
    public void initRewardStock(Cmpt5159SummerAdventureRewardStock stock) {
        // 先尝试查询是否已存在
        Cmpt5159SummerAdventureRewardStock query = new Cmpt5159SummerAdventureRewardStock();
        query.setActId(stock.getActId());
        query.setCmptUseInx(stock.getCmptUseInx());
        query.setGridNo(stock.getGridNo());
        
        List<Cmpt5159SummerAdventureRewardStock> existing = 
                gameecologyDao.select(Cmpt5159SummerAdventureRewardStock.class, query);
        
        if (existing.isEmpty()) {
            gameecologyDao.insert(stock);
        }
    }
}
