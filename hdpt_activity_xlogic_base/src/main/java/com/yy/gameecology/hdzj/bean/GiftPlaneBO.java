package com.yy.gameecology.hdzj.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2021/7/2
 */
@Getter
@Setter
@ToString
public class GiftPlaneBO {

    private Integer count;
    /**
     * 送礼uid
     */
    @JsonProperty("uid")
    private Long sendUid;
    /**
     * 收礼uid
     */
    @JsonProperty("anchor_uid")
    private Long recvUid;
    /**
     * 礼物id
     */
    @JsonProperty("gift_id")
    private String giftId;
    /**
     * 礼物数量
     */
    @JsonProperty("gift_num")
    private Long giftNum;
    /**
     * 顶级频道号
     */
    private Long sid;
    /**
     * 子频道号
     */
    private Long ssid;

    public GiftPlaneBO(int count, Long sendUid, Long recvUid, String giftId, Long giftNum, Long sid, Long ssid){
        this.count = count;
        this.sendUid = sendUid;
        this.recvUid = recvUid;
        this.giftId = giftId;
        this.giftNum = giftNum;
        this.sid = sid;
        this.ssid = ssid;
    }

    public GiftPlaneBO() {

    }

    /**
     * 提取简要信息
     */
    public String outline() {
        return String.format("GiftPlaneBO[sendUid:%s, recvUid:%s, sid:%s, %s %s %s]",
                sendUid, recvUid, sid, giftId, giftNum, count);
    }

}
