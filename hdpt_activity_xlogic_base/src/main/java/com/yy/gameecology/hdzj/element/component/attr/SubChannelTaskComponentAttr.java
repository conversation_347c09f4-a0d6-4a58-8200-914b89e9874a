package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.annotation.SkipCheck;
import com.yy.gameecology.hdzj.bean.BroadcastConfig;
import com.yy.gameecology.hdzj.bean.SunshineTaskConfig;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.attrconfig.TimeKeySource;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 强厅任务
 */
@SkipCheck
@Data
public class SubChannelTaskComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "榜单id")
    private long rankId;

    @ComponentAttrField(labelText = "阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "任务开始时间")
    private Date startTime;


    @ComponentAttrField(labelText = "时间分榜", dropDownSourceBeanClass = TimeKeySource.class)
    private long timeKey;

    @ComponentAttrField(labelText = "奖池数量")
    private long awardPoolConfig;

    @ComponentAttrField(labelText = "任务消息重试时限", remark = "超过这个时间，榜单更新更新事件 redis seq失效不可安全重试")
    private Integer seqExpireSeconds = DateUtil.ONE_DAY_SECONDS;

    @ComponentAttrField(labelText = "榜单结算任务资格设置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "榜单Id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "阶段Id"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "任务类型")
            })
    private Map<Long, Map<Long, String>> promoteMemberTaskType = Maps.newHashMap();

    @ComponentAttrField(labelText = "最小过任务分值")
    private long minPassValue;


    @ComponentAttrField(labelText = "任务配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "任务类型"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "任务等级"),
                    @SubField(fieldName = Constant.VALUE, type = SunshineTaskConfig.class, labelText = "过任务配置")
            })
    private Map<String, Map<Long, SunshineTaskConfig>> taskConfig;


    @ComponentAttrField(labelText = "过任务广播配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "任务类型"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "任务等级"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = BroadcastConfig.class, labelText = "任务等级")
            })
    private Map<String, Map<Long, List<BroadcastConfig>>> taskBanner;


    @ComponentAttrField(labelText = "默认横幅", remark = "过任务横幅默认url,如果levelBro没有配置则使用这个默认的")
    private String defaultBannerUrl;


    @ComponentAttrField(labelText = "任务提示文案")
    private String awardDescTips = "今日荣耀值达%s可完成%s任务";


    @ComponentAttrField(labelText = "角色奖励日限",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "角色Id"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "奖励日限额", remark = "小于等于0代表不限制")
            })
    private Map<Long, Long> roleAwardDayLimit;


    @ComponentAttrField(labelText = "奖励的奖品",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "busiId"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "taskId"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "packageId")
            })
    private Map<Long, Map<Long, Long>> busiTaskIdPackageId = Maps.newHashMap();
}
