package com.yy.gameecology.hdzj.bean;

import com.yy.gameecology.hdzj.element.history.attr.SunshineTaskDailyAwardComponentAttr;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-11-04 11:34
 **/
public class SunShineTaskAwardInvoke {
    private String taskTime;
    private SunshineTaskDailyAwardComponentAttr attr;

    public SunShineTaskAwardInvoke(String taskTime, SunshineTaskDailyAwardComponentAttr attr) {
        this.taskTime = taskTime;
        this.attr = attr;
    }

    public String getTaskTime() {
        return taskTime;
    }

    public void setTaskTime(String taskTime) {
        this.taskTime = taskTime;
    }

    public SunshineTaskDailyAwardComponentAttr getAttr() {
        return attr;
    }

    public void setAttr(SunshineTaskDailyAwardComponentAttr attr) {
        this.attr = attr;
    }
}
