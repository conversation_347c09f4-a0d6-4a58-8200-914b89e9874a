package com.yy.gameecology.hdzj.element.component;


import com.alibaba.fastjson.JSON;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardLotteryMsg;
import com.yy.gameecology.activity.service.BroadCastHelpService;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AwardBroadcastComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.BusiId;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: CXZ
 * @Desciption: 奖品广播组件 大奖广播(一般是单业务全频道广播)
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
@Component
public class AwardBroadcastComponent extends BaseActComponent<AwardBroadcastComponentAttr> {

    @Autowired
    private BroadCastHelpService broadCastHelpService;

    @Override
    public Long getComponentId() {
        return ComponentId.AWARD_BROADCAST;
    }

    @HdzjEventHandler(value = HdztAwardLotteryMsg.class, canRetry = false)
    public void broadcastAward(HdztAwardLotteryMsg event, AwardBroadcastComponentAttr attr) {

        long taskId = event.getTaskId();
        long busiId = event.getBusiId();
        long actId = attr.getActId();
        long uid = event.getUid();
        if (attr.getBusiId() != busiId || !ArrayUtils.contains(attr.getTaskIds(), taskId)) {
            return;
        }

        long[] broAttrPackageIds = attr.getPackageIds();
        List<HdztAwardLotteryMsg.Award> awardList = event.getData().stream()
                .filter(award -> ArrayUtils.isEmpty(broAttrPackageIds) || ArrayUtils.contains(broAttrPackageIds, award.getPackageId()))
                .collect(Collectors.toList());

        if (awardList.isEmpty()) {
            return;
        }
        log.info("broadcastAward event:{}", JSON.toJSONString(event));
        BusiId busiIdEnum = BusiId.findByValue(attr.getBroBusiId());
        if (attr.isMageAward()) {
            String content = awardList.stream().map(HdztAwardLotteryMsg.Award::getGiftName).collect(Collectors.joining(attr.getSeparator()));
            GameecologyActivity.GameEcologyMsg msg = getMessage(actId, uid, content, busiIdEnum);
            log.info("mageAward bro:{}", JsonFormat.printToString(msg));
            broadCastHelpService.broadcast(actId, busiIdEnum, attr.getBroadcastType(), 0L, 0L, msg);
        } else {
            awardList.stream().forEach(award -> {
                GameecologyActivity.GameEcologyMsg msg = getMessage(actId, uid, award.getGiftName(), busiIdEnum);
                log.info("award bro:{}", JsonFormat.printToString(msg));
                broadCastHelpService.broadcast(actId, busiIdEnum, attr.getBroadcastType(), 0L, 0L, msg);
            });
        }


    }

    private GameecologyActivity.GameEcologyMsg getMessage(long actId, long uid, String content, BusiId busiId) {
        GameecologyActivity.Act202008_UserLotteryBanner.Builder lotteryBanner = GameecologyActivity.Act202008_UserLotteryBanner.newBuilder()
                .setActId(actId).setUser(broadCastHelpService.getUserInfo(uid, busiId)).setGift(GameecologyActivity.GiftInfo.newBuilder().setName(content));

        return GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kAct202008_UserLotteryBanner_VALUE)
                .setAct202008UserLotteryBanner(lotteryBanner).build();
    }


}
