package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Getter
@Setter
public class FtsHallCPComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "过滤弹幕游戏频道")
    protected boolean excludeDanmaku = true;

    @ComponentAttrField(labelText = "礼物ID", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class), remark = "多个使用英文逗号隔开")
    protected Set<String> giftIds = Collections.emptySet();

    @ComponentAttrField(labelText = "登层最少单笔价值", remark = "登一层所需的最小单笔送礼金额（单位：毛）")
    protected long threshold;

    @ComponentAttrField(labelText = "可获奖层数", remark = "可以获得奖励的基础层数")
    protected long awardStep = 100;

    @ComponentAttrField(labelText = "发奖奖池ID")
    protected long rewardTaskId;

    @ComponentAttrField(labelText = "奖品列表", subFields = @SubField(labelText = "奖品配置", fieldName = Constant.LIST_VALUE_TYPE, type = RewardItem.class))
    protected List<RewardItem> rewardItems;

    @ComponentAttrField(labelText = "每日价值上限", subFields = {@SubField(labelText = "日期", fieldName = Constant.KEY1, type = String.class, remark = "yyyyMMdd格式"),
            @SubField(labelText = "上限", fieldName = Constant.VALUE, type = Integer.class)})
    protected Map<String, Integer> dailyWorthLimit;

    @ComponentAttrField(labelText = "日榜ID", remark = "用于每日总限额流转，无实际用处")
    protected long rollingRankId;

    @ComponentAttrField(labelText = "日榜阶段ID", remark = "用于每日总限额流转，无实际用处")
    protected long rollingPhaseId;

    @ComponentAttrField(labelText = "休息日期", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Integer.class), remark = "玩法暂停日期（yyyyMMdd），多个使用英文逗号隔开")
    protected Set<Integer> breakDates;

    @ComponentAttrField(labelText = "玩法开启日期", remark = "包含，格式：yyyyMMdd")
    protected int startDate;

    @ComponentAttrField(labelText = "玩法结束日期", remark = "包含，格式：yyyyMMdd")
    protected int endDate;

    @ComponentAttrField(labelText = "主持通知文案", remark = "主持玩法开启后抽次开播单播文案")
    protected String firstNoticeMsg;

    @ComponentAttrField(labelText = "登层文案", remark = "殿堂CP登层后的文案，预留%s和%d替换昵称和登的层数")
    protected String gradeNoticeTpl;

    /**
     * svga点击按钮key name
     */
    @ComponentAttrField(labelText = "svga点击按钮key name")
    private String clickLayerName;
    /**
     * 横幅svga url
     */
    @ComponentAttrField(labelText = "不带围观按钮的横幅svga", remark = "不带围观按钮的svga")
    private String svgaURL;

    /**
     * 横幅svga url
     * 需要跳转，就设置jump==1，然后把带围观和不带围观的url都填上，会处理当前频道不带围观，其他频道带围观
     */
    @ComponentAttrField(labelText = "带围观按钮的横幅svga", remark = "带围观按钮的svga需要跳转，就设置jump==1，然后把带围观和不带围观的url都填上，会处理当前频道不带围观，其他频道带围观")
    private String jumpSvgaURL;

    /**
     * 礼物滚屏url
     */
    @ComponentAttrField(labelText = "不带围观的礼物滚屏svga", remark = "当前端限制横幅时,前端将使用这个svga播放在礼物滚屏")
    private String miniURL;

    /**
     * 横幅svga url
     */
    @ComponentAttrField(labelText = "带围观按钮的礼物滚屏svga", remark = "带围观按钮的礼物滚屏svga,前端将使用这个svga播放在礼物滚屏")
    private String jumpMiniURL;

    @ComponentAttrField(labelText = "svga文案配置",
            subFields = @SubField(labelText = "文案配置", fieldName = Constant.LIST_VALUE_TYPE, type = BannerSvgaTextConfig.class),
            remark = "文案可包含占位符:{userNick}-用户昵称，{anchorNick}-主持昵称，{rewardCount}-奖励数量，{rewardUnit}-奖励单位")
    protected List<BannerSvgaTextConfig> svgaTexts;

    @ComponentAttrField(labelText = "svga图片配置", subFields = {
            @SubField(labelText = "svga Key", fieldName = Constant.KEY1, type = String.class),
            @SubField(labelText = "图片配置", fieldName = Constant.VALUE, type = String.class,
                    remark = "可包含占位符:{userAvatar}-用户头像，{anchorAvatar}-主持头像，{rewardIcon}-奖励图片")
    })
    protected Map<String, String> svgaImages;

    @ComponentAttrField(labelText = "循环次数", remark = "循环播放次数, 0-无限循环(勿填0)")
    private int loops = 1;

    @ComponentAttrField(labelText = "布局", remark = " 横幅类型(contentType)==6使用 可选 动画播放位置（左右充满，垂直对齐类型）0：全屏播放；1：居中对齐播放；2：顶部对齐播放；3：底部对齐播放")
    private int layoutType;

    @ComponentAttrField(labelText = "布局边距", remark = "相对父布局的间距 2个元素的数组，分别对应顶部和底部的间距；对应位置为[top, bottom]。通常top、bottom为0。配置例子：{\"android\":[10,0],\"ios\":[0,0]}")
    private String layoutMargin = "{\"android\":[10,0],\"ios\":[0,0]}";


    @ComponentAttrField(labelText = "宽高比", remark = "必填 宽高比 客户端默认宽为全屏，svga高度根据宽高比计算,填写实例：6:9")
    private String whRatio;

    @Getter
    @Setter
    public static class RewardItem {
        @ComponentAttrField(labelText = "发奖奖包ID")
        protected long rewardPackageId;

        @ComponentAttrField(labelText = "名称")
        protected String rewardName;

        @ComponentAttrField(labelText = "图标", propType = ComponentAttrCollector.PropType.IMAGE)
        protected String rewardIcon;

        @ComponentAttrField(labelText = "数量")
        protected int rewardCount;

        @ComponentAttrField(labelText = "单位")
        protected String rewardUnit;

        @ComponentAttrField(labelText = "价值")
        protected int worth;

        @ComponentAttrField(labelText = "同一CP单日上限")
        protected int limit;

        @ComponentAttrField(labelText = "概率值")
        protected int probability;
    }
}
