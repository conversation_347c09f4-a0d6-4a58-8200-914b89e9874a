package com.yy.gameecology.hdzj.element.component.bean;

import lombok.Data;

import java.util.List;

/**
 * 夏日探险抽奖响应
 *
 * <AUTHOR> Assistant
 * @date 2025-07-02
 */
@Data
public class SummerAdventureDrawResponse {

    /**
     * 是否执行了抽奖
     */
    private Boolean executed;

    /**
     * 起始位置
     */
    private Integer startPosition;

    /**
     * 结束位置
     */
    private Integer endPosition;

    /**
     * 骰子结果列表
     */
    private List<DiceResult> diceResults;

    /**
     * 奖励列表
     */
    private List<RewardResult> rewards;

    /**
     * 剩余骰子数
     */
    private Long remainingDice;

    /**
     * 累计骰子数
     */
    private Long totalDice;

    /**
     * 双方确认状态
     */
    private ConfirmStatus confirmStatus;

    @Data
    public static class DiceResult {
        /**
         * 骰子点数
         */
        private Integer points;

        /**
         * 移动到的格子
         */
        private Integer targetGrid;

        /**
         * 是否有奖励
         */
        private Boolean hasReward;
    }

    @Data
    public static class RewardResult {
        /**
         * 格子编号
         */
        private Integer gridNo;

        /**
         * 奖励名称
         */
        private String rewardName;

        /**
         * 奖励图标
         */
        private String rewardIcon;

        /**
         * 奖励类型
         */
        private Integer rewardType;

        /**
         * 奖励数量
         */
        private Integer rewardCount;

        /**
         * 奖励价值
         */
        private Long rewardValue;

        /**
         * 是否大奖
         */
        private Boolean isBigReward;

        /**
         * 奖池ID
         */
        private Long taskId;

        /**
         * 奖包ID
         */
        private Long packageId;
    }

    @Data
    public static class ConfirmStatus {
        /**
         * 用户是否确认
         */
        private Boolean userConfirmed;

        /**
         * 主播是否确认
         */
        private Boolean anchorConfirmed;

        /**
         * 是否双方都确认
         */
        private Boolean bothConfirmed;
    }
}
