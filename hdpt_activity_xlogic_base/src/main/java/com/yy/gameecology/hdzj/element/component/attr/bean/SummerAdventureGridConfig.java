package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * 夏日探险格子配置
 *
 * <AUTHOR> Assistant
 * @date 2025-07-02
 */
@Data
public class SummerAdventureGridConfig {

    @ComponentAttrField(labelText = "格子编号", remark = "1-30")
    private Integer gridNo;

    @ComponentAttrField(labelText = "奖励名称")
    private String rewardName;

    @ComponentAttrField(labelText = "奖励类型", remark = "0-无奖励 1-头像框 2-气泡 3-礼物 4-入场秀")
    private Integer rewardType;

    @ComponentAttrField(labelText = "奖励时长", remark = "单位：天，仅头像框、气泡、入场秀需要")
    private Integer rewardDuration;

    @ComponentAttrField(labelText = "奖励数量", remark = "礼物类奖励的数量")
    private Integer rewardCount;

    @ComponentAttrField(labelText = "奖励价值", remark = "单位：厘")
    private Long rewardValue;

    @ComponentAttrField(labelText = "奖励图标URL")
    private String rewardIcon;

    @ComponentAttrField(labelText = "奖池ID", remark = "发奖用的奖池ID")
    private Long taskId;

    @ComponentAttrField(labelText = "奖包ID", remark = "发奖用的奖包ID")
    private Long packageId;

    @ComponentAttrField(labelText = "初始库存", remark = "礼物类奖励的初始库存，-1表示无限")
    private Long initialStock = -1L;

    @ComponentAttrField(labelText = "骰子阈值", remark = "每累计多少个骰子增加库存")
    private Long diceThreshold;

    @ComponentAttrField(labelText = "库存增量", remark = "达到阈值时增加的库存数量")
    private Long stockIncrement;

    @ComponentAttrField(labelText = "是否大奖", remark = "1-是 0-否，大奖会触发全服广播")
    private Integer isBigReward = 0;
}
