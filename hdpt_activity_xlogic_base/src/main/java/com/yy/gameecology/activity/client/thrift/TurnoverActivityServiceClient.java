package com.yy.gameecology.activity.client.thrift;

import com.yy.thrift.turnover.TActivityService;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-15 15:32
 **/
@Component
public class TurnoverActivityServiceClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

   @Reference(protocol = "nythrift_compact", owner = "${turnoverService_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"})
   public TActivityService.Iface proxy = null;

   @Reference(protocol = "nythrift_compact", owner = "${turnoverService_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"}
           , retries = 2 , cluster = "failover")
   public TActivityService.Iface readProxy = null;

   public TActivityService.Iface getProxy() {
      return proxy;
   }

   public TActivityService.Iface getReadProxy() {
      return readProxy;
   }
}
