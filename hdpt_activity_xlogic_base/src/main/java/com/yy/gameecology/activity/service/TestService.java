package com.yy.gameecology.activity.service;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.ActAwardRecord;
import com.yy.gameecology.common.utils.Clock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * desc:测试专用
 *
 * @createBy 曾文帜
 * @create 2021-01-27 17:42
 **/
@Service
public class TestService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    @Autowired
    private GameecologyDao gameecologyDao;


    public String batchInsertAwardRecordTest(int amount) {
        if (true) {
            return "skip";
        }
        List<ActAwardRecord> records = Lists.newArrayList();
        for (int i = 0; i <= amount; i++) {
            ActAwardRecord awardRecord = new ActAwardRecord();
            awardRecord.setTaskId(202101270000001L);
            Date now = new Date();
            awardRecord.setSeq(now.getTime() + "");
            awardRecord.setPackageId(1L);
            awardRecord.setPackageNum(1000L);
            awardRecord.setPackageName("Y币");
            awardRecord.setUid(50042952L);
            awardRecord.setStatus(0L);
            awardRecord.setPlatform(0L);
            awardRecord.setAddress("2020-10-12");
            awardRecord.setRemark("用户积分:127240,总奖池积分:127240,rankId:86");
            awardRecord.setCtime(new Date());
            awardRecord.setUtime(new Date());

            records.add(awardRecord);
        }
        Clock clock = new Clock();
        gameecologyDao.batchInsert(ActAwardRecord.class, records, ActAwardRecord.TABLE_NAME);
        String msg = String.format("batchInsertAwardRecordTest,acount:%s,cost:%s", amount, clock.tag());
        log.info(msg);

        return msg;

    }


}
