package com.yy.gameecology.activity.client.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.duowan.udb.util.GsonUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.yy.gameecology.activity.bean.mms.*;
import com.yy.gameecology.hdzj.utils.SendDataClient;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class MmsTextBulkReporterClient {

    private static final Logger logger = LoggerFactory.getLogger(MmsTextBulkReporterClient.class);

    @Value("${audit.mms.text.bulk.url}")
    private String textBulkUrl;

    private Gson gson = new GsonBuilder().serializeNulls().create();

    @Autowired
    private RestTemplate restTemplate;

    protected AttcType getMmsType() {
        return AttcType.TEXT;
    }

    public ReportResult report(List<MmsReport> reports, String appId, String appSecret) {
        logger.info("report reports:{} appId:{},appSecret:{}", JSON.toJSONString(reports), appId,appSecret);
        ReportResult rs = ReportResult.SUCCESS;
        try {
            BulkTextMmsReport report = null;
            long uid = 0L;
            if (reports.get(0) instanceof BulkTextMmsReport){
                report = (BulkTextMmsReport)reports.get(0);
                uid = report.getUid();
            }else{
                rs = ReportResult.REPORT_FAIL;
                return rs;
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            TextBulk textBulk = report.getTextBulk();
            textBulk.setAccount(String.valueOf(uid));
            textBulk.setSign(SendDataClient.getSign(gson.toJson(textBulk), appSecret));
            textBulk.setCallback(report.getCallback());
            String textBulkJson = gson.toJson(textBulk);
            Map<String,String> param =  gson.fromJson(textBulkJson, new TypeToken<Map<String, String>>() {}.getType());

            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.setAll(param);
            HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity(body, headers);
            logger.info("request:{}",textBulkUrl);
            ResponseEntity<String> response = restTemplate.postForEntity(textBulkUrl, httpEntity, String.class);


            String bodyText = response.getBody();
            logger.info("report params:{}, bodyText:{}", GsonUtil.toJson(param), bodyText);
            Map<String, Object> mapResult = new HashMap<>(JSON.parseObject(bodyText));
            logger.info("report mapResult:{}",mapResult);
            int code = MapUtils.getInteger(mapResult, "code", 0);
            int successCode = 100;
            if (code == successCode) {
                String bulk = MapUtils.getString(mapResult, "bulk");
                if (StringUtils.isNotBlank(bulk)){
                    JSONArray array = JSONObject.parseArray(bulk);
                    for (int i = 0; i < array.size(); i++){
                        JSONObject obj = (JSONObject)array.get(i);
                        int subCode = obj.getInteger("code");
                        if (subCode == successCode){
                            JSONObject result = obj.getJSONObject("result");
                            if (result != null){
                                int status = result.getInteger("status");
                                int hit = 2;
                                if(status == hit) {
                                    rs = ReportResult.VIOLATION;
                                    break;
                                }
                            }

                        }
                    }
                }

            }else{
                logger.error("MmsTextBulkReporter report fail with code:{}, message:{}", code, mapResult.get("message"));
                rs = ReportResult.REPORT_FAIL;
            }
        } catch (Exception e) {
            rs = ReportResult.REQUEST_FAIL;
            logger.error("report error " + e.getMessage(), e);
        }
        return rs;
    }
}
