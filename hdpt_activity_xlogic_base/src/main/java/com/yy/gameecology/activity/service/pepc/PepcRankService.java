package com.yy.gameecology.activity.service.pepc;

import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.common.consts.PepcConst;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.component.attr.PepcPhaseComponentAttr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-08 10:55
 **/
@Service
public class PepcRankService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private CommonDataDao commonDataDao;


    public long getRankActId(PepcPhaseComponentAttr attr) {
        String value = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), PepcConst.ActInfoField.ACT_INFO_KEY, PepcConst.ActInfoField.RANK_ACT_ID);
        if(StringUtil.isBlank(value)){
            return attr.getPreActId();
        }
        return Convert.toLong(value, attr.getPreActId());
    }

    public int saveRankActId(PepcPhaseComponentAttr attr, long actId) {
        return commonDataDao.hashValueSet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), PepcConst.ActInfoField.ACT_INFO_KEY, PepcConst.ActInfoField.RANK_ACT_ID, Convert.toString(actId));
    }
}
