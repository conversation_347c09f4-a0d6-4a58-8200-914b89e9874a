package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.actlayer.PhaseInfo;
import com.yy.gameecology.activity.bean.hdzt.RankPhasePair;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.ActRoleRankMap;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.hdztranking.QueryRankIdItem;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * desc: 用于定位浮层挂件角色当前榜单id、阶段id
 *
 * @createBy 曾文帜
 * @create 2020-09-15 14:30
 **/
@Service
public class ActRoleRankMapService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    // key actId_itemTypeKey_roleId_rankMapType
    private Map<String, List<ActRoleRankMap>> actRoleRankMapMap = Maps.newConcurrentMap();

    @Autowired
    private GameecologyDao gameecologyDao;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;


    @PostConstruct
    @Scheduled(cron = "0 0/5 * * * ? ")
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    public void init() {
        List<ActRoleRankMap> actRoleRankMaps = gameecologyDao.getAllEffectActRoleRankMap();
        Map<String, List<ActRoleRankMap>> tmpMap = Maps.newConcurrentMap();

        for (ActRoleRankMap actRoleRankMap : actRoleRankMaps) {
            String[] roleIdArray = actRoleRankMap.getRoleIds().split(",");
            for (String roleId : roleIdArray) {
                String key = getActRoleRankMapKey(actRoleRankMap.getActId(), actRoleRankMap.getItemTypeKey(), roleId.trim(), actRoleRankMap.getRankMapType());
                List<ActRoleRankMap> list = tmpMap.getOrDefault(key, Lists.newArrayList());
                list.add(actRoleRankMap);
                tmpMap.put(key, list);
            }
        }


        actRoleRankMapMap = tmpMap;
    }

    private String getActRoleRankMapKey(Long actId, String itemTypeKey, Long roleId, Long rankMapType) {
        return getActRoleRankMapKey(actId, itemTypeKey, roleId + "", rankMapType);
    }

    private String getActRoleRankMapKey(Long actId, String itemTypeKey, String roleId, Long rankMapType) {
        return String.format("%s_%s_%s_%s", actId, itemTypeKey, roleId, rankMapType);
    }

    /**
     * 榜单阶段映射关系，结束时间用展示结束时间
     *
     * @param actId  活动id
     * @param roleId 角色id
     * @return 榜单id
     */
    public Long getActLayerRankId(Long actId, String itemTypeKey, String memberId, Long roleId, Long rankMapType, Date now) {
        ActRoleRankMap actRoleRankMap = getActRoleRankMap(actId, itemTypeKey, memberId, roleId, rankMapType, now);
        if (actRoleRankMap == null) {
            return 0L;
        }

        return actRoleRankMap.getRankId();
    }


    /**
     * @param memberId 如果需要用 rankTypeRange动态查找榜单的时候，memberId必填
     */
    public ActRoleRankMap getActRoleRankMap(Long actId, String itemTypeKey, String memberId, Long roleId, Long rankMapType, Date now) {

        String key = getActRoleRankMapKey(actId, itemTypeKey, roleId, rankMapType);
        List<ActRoleRankMap> actRoleRankMaps = actRoleRankMapMap.get(key);
        if (CollectionUtils.isEmpty(actRoleRankMaps)) {
            return null;
        }
        //优先找明确配置了rankId 和 phaseId的数据
        ActRoleRankMap map = findDefiniteActRoleRankMap(actRoleRankMaps, actId, now);
        if (map != null) {
            return map;
        }

        //模糊查找 rankId 和 phaseId的数据
        return findUnDefiniteActRoleRankMap(actRoleRankMaps, actId, memberId, roleId, now);
    }

    /**
     * 根据 活动id + 角色id + 映射类型 + 阶段展示开始结束时间  推断出配置, phaseId 配置为-1时，代表全局时间，优先返回
     */
    private ActRoleRankMap findDefiniteActRoleRankMap(List<ActRoleRankMap> actRoleRankMaps, Long actId, Date now) {
        List<ActRoleRankMap> definiteMap = actRoleRankMaps.stream()
                .filter(x -> Convert.toLong(x.getPhaseId(), 0) != 0 && Convert.toLong(x.getRankId(), 0) != 0)
                .collect(Collectors.toList());
        for (ActRoleRankMap map : definiteMap) {
            Long phaseId = map.getPhaseId();
            //代表全局时间适用
            if (phaseId == -1) {
                return map;
            }

            PhaseInfo phaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, phaseId);
            if (phaseInfo == null) {
                continue;
            }
            if (now.getTime() >= phaseInfo.getShowBeginTime() && now.getTime() <= phaseInfo.getShowEndTime()) {
                return map;
            }
        }

        return null;
    }

    private ActRoleRankMap findUnDefiniteActRoleRankMap(List<ActRoleRankMap> actRoleRankMaps, Long actId, String memberId, Long roleId, Date now) {
        List<ActRoleRankMap> unDefiniteMap = actRoleRankMaps.stream()
                .filter(x -> Convert.toLong(x.getPhaseId(), 0) == 0 || Convert.toLong(x.getRankId(), 0) == 0)
                .collect(Collectors.toList());
        for (ActRoleRankMap map : unDefiniteMap) {
            long phaseId = Convert.toLong(map.getPhaseId(), 0);
            List<Long> effectPhaseId = Lists.newArrayList();
            if (phaseId != 0) {
                PhaseInfo phaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, phaseId);
                if (phaseInfo != null && now.getTime() >= phaseInfo.getShowBeginTime() && now.getTime() <= phaseInfo.getShowEndTime()) {
                    effectPhaseId.add(phaseId);
                }
            } else if (StringUtil.isNotBlank(map.getPhaseIdRange())) {
                String[] phaseIds = map.getPhaseIdRange().split(",");
                for (String phaseIdStr : phaseIds) {
                    PhaseInfo phaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, Convert.toLong(phaseIdStr.trim()));
                    if (phaseInfo != null && now.getTime() >= phaseInfo.getShowBeginTime() && now.getTime() <= phaseInfo.getShowEndTime()) {
                        effectPhaseId.add(phaseInfo.getPhaseId());
                    }
                }
            }
            //匹配到没有有效时间内的phaseId
            if (CollectionUtils.isEmpty(effectPhaseId)) {
                continue;
            }

            //如果明确指定了榜单id,且匹配到了有效期内的阶段id,则直接返回
            long rankId = Convert.toLong(map.getRankId(), 0);
            if (rankId > 0 && effectPhaseId.size() == 1) {
                return returnActRoleRankMap(map, rankId, effectPhaseId.get(0));
            }

            //动态查找榜单id需要指定成员
            if (StringUtil.isBlank(memberId)) {
                return null;
            }

            List<QueryRankIdItem> para = Lists.newArrayList();
            for (Long phaseItem : effectPhaseId) {
                QueryRankIdItem item = new QueryRankIdItem();
                item.setMemberId(memberId);
                item.setPhaseId(phaseItem);
                item.setMemberType(roleId + "");
                para.add(item);
            }
            Map<String, List<RankPhasePair>> rankIdMap = hdztRankingThriftClient.queryRankPhasePair(actId, para);
            if (MapUtils.isEmpty(rankIdMap) || !rankIdMap.containsKey(memberId)) {
                continue;
            }
            List<RankPhasePair> rankPhasePairs = rankIdMap.get(memberId);
            List<String> rankIdRange = Lists.newArrayList();
            CollectionUtils.addAll(rankIdRange, map.getRankIdRange().split(","));

            List<RankPhasePair> effectPair = rankPhasePairs.stream()
                    .filter(x -> rankIdRange.contains(x.getRankId() + "") && effectPhaseId.contains(x.getPhaseId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(effectPair)) {
                continue;
            }
            if (effectPair.size() > 1) {
                log.warn("found more than one pair,actId:{},roleId:{},memberId:{},map:{},effectPair:{}"
                        , actId, roleId, memberId, JSON.toJSONString(map), JSON.toJSONString(effectPair));
                continue;
            }

            return returnActRoleRankMap(map, effectPair.get(0).getRankId(), effectPair.get(0).getPhaseId());
        }

        return null;
    }


    /**
     * 创建新对象返回，防止缓存数据被更改
     */
    private ActRoleRankMap returnActRoleRankMap(ActRoleRankMap source, long newRankId, long newPhaseId) {
        ActRoleRankMap actRoleRankMap = new ActRoleRankMap();
        BeanUtils.copyProperties(source, actRoleRankMap);
        actRoleRankMap.setRankId(newRankId);
        actRoleRankMap.setPhaseId(newPhaseId);
        return actRoleRankMap;
    }

    public ActRoleRankMap getActRoleRankMap(Long actId, String itemTypeKey, Long roleId, Long rankId, Long rankMapType, Date now) {
        String key = getActRoleRankMapKey(actId, itemTypeKey, roleId, rankMapType);
        List<ActRoleRankMap> actRoleRankMaps = actRoleRankMapMap.get(key);
        if (CollectionUtils.isEmpty(actRoleRankMaps)) {
            return null;
        }

        for (ActRoleRankMap actRoleRankMap : actRoleRankMaps) {
            if (!actRoleRankMap.getRankId().equals(rankId)) {
                continue;
            }
            Long phaseId = actRoleRankMap.getPhaseId();
            if (phaseId == -1) {
                return actRoleRankMap;
            }

            PhaseInfo phaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, phaseId);
            if (phaseInfo == null) {
                continue;
            }
            if (now.getTime() >= phaseInfo.getShowBeginTime() && now.getTime() <= phaseInfo.getShowEndTime()) {
                return actRoleRankMap;
            }
        }

        return null;
    }


    //找到上一阶段 榜单映射配置
    public ActRoleRankMap getActRolePrePhaseRankMap(Long actId, String itemTypeKey, Long roleId, Long rankMapType, Date now) {
        String key = getActRoleRankMapKey(actId, itemTypeKey, roleId, rankMapType);
        List<ActRoleRankMap> actRoleRankMapList = actRoleRankMapMap.get(key);
        if (CollectionUtils.isEmpty(actRoleRankMapList)) {
            return null;
        }

        Map<Long, ActRoleRankMap> actRoleRankMap = Maps.newLinkedHashMap();
        for (ActRoleRankMap item : actRoleRankMapList) {
            actRoleRankMap.put(item.getPhaseId(), item);
        }

        List<PhaseInfo> phaseInfos = Lists.newArrayList();
        PhaseInfo prePhaseInfo = null;
        Map<Long, PhaseInfo> actPhaseMap = hdztRankingThriftClient.queryRankingPhaseMap(actId);

        for (Long phaseId : actPhaseMap.keySet()) {

            PhaseInfo phaseInfo = actPhaseMap.get(phaseId);
            ActRoleRankMap phaseMap = actRoleRankMap.get(phaseId);
            if (phaseMap == null) {
                continue;
            }
            if (phaseMap.getPhaseId() == -1) {
                return phaseMap;
            }
            //取最末尾1个，就是时间最近的1个阶段
            if (CollectionUtils.isNotEmpty(phaseInfos)
                    && now.getTime() >= phaseInfo.getShowBeginTime() && now.getTime() <= phaseInfo.getShowEndTime()) {
                prePhaseInfo = phaseInfos.get(phaseInfos.size() - 1);
                break;
            }
            phaseInfos.add(phaseInfo);
        }


        if (prePhaseInfo == null) {
            return null;
        }

        return actRoleRankMap.get(prePhaseInfo.getPhaseId());
    }
}
