package com.yy.gameecology.activity.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.FtsRoomMgrInfoVo;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.client.thrift.*;
import com.yy.gameecology.common.bean.ChannelBaseInfo;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.client.WebdbServiceClient;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.WebdbChannelInfo;
import com.yy.java.webdb.WebdbSubChannelInfo;
import com.yy.thrift.act_ext_support.MemberItemInfo;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import com.yy.thrift.zhuiwan_newfamily.FamilyBasicInfo;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-09-19 15:48
 **/
@Service
public class MemberInfoService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CommonService commonService;

    @Autowired
    private OnlineSubChannelCacheService onlineSubChannelInfoService;


    @Autowired
    private PeiwanThriftClient peiwanThriftClient;

    @Autowired
    private FtsRecommendDataThriftClient ftsRecommendDataThriftClient;

    @Autowired
    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;

    @Autowired
    private FtsRoomManagerThriftClient ftsRoomManagerThriftClient;

    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;

    @Autowired
    private WebdbServiceClient webdbServiceClient;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    public String getMemberName(Long busiId,
                                RoleType roleType,
                                String memberId) {

        MemberInfo memberInfo = getMemberInfo(busiId, roleType, memberId);
        if (memberInfo != null) {
            return memberInfo.getName();
        }
        return "";
    }

    //读取成员信息 公会/子频道/陪玩团
    //#N TODO 这里应该要 结合榜单配置来取信息，不固定逻辑
    public MemberInfo getMemberInfo(Long busiId,
                                    RoleType roleType,
                                    String memberId) {

        MemberInfo memberInfo = null;

        switch (roleType) {
            case GUILD:
                if (busiId != BusiId.PEI_WAN.getValue()) {
                    memberInfo = getChannel(memberId, roleType);
                }
                //陪玩频道特殊处理
                else {
                    memberInfo = getPwChannel(memberId);
                }
                break;

            case HALL:
                memberInfo = getSubChannel(memberId, roleType);
                if (busiId == BusiId.MAKE_FRIEND.getValue()) {
                    Map<String, String> roleMemberPicMap = ftsRecommendDataThriftClient.batchGetRoomMgrPicByChannel(Lists.newArrayList(memberId));
                    // boss后台配置的推荐图,key:sid_ssid
                    Map<String, String> recommendPicMap = ftsRecommendDataThriftClient.batchGetRecommendConfigPicture(Lists.newArrayList(memberId), changeToBusinessType(busiId));
                    setAvatar(memberInfo, roleMemberPicMap.get(memberId), recommendPicMap.get(memberId));

                    //房管厅优先展示取房管后台的昵称
                    FtsRoomMgrInfoVo mgrInfoVo = ftsRoomManagerThriftClient.ftsGetRoomMgrInfoByCh(memberId);
                    if (mgrInfoVo != null) {
                        memberInfo.setName(mgrInfoVo.getName());
                    }
                }
                break;

            case PWTUAN:
                memberInfo = getPwTeam(memberId);
                break;
            case ROOM:
                memberInfo = getRoomInfo(memberId);
                break;
            case WAITER:
            case USER:
            case ANCHOR:
                // 交友以后和榜单一致，读取YY头像、昵称
//                if (busiId == BusiId.MAKE_FRIEND.getValue()) {
//                    UserInfo userInfo = ftsBaseInfoBridgeClient.getFtsUserInfo(Convert.toLong(memberId, 0));
//                    memberInfo = new MemberInfo();
//                    memberInfo.setLogo(userInfo.getAvatar_url());
//                    memberInfo.setName(userInfo.getNick());
//                    memberInfo.setHdLogo(userInfo.getAvatar_url());
//                } else {
//                }
                memberInfo = getUserInfo(Convert.toLong(memberId, 0));
                break;
            case FAMILY:
                memberInfo = new MemberInfo();
                FamilyBasicInfo familyBasicInfo = zhuiWanPrizeIssueServiceClient.getFamilyBasicInfo(Convert.toLong(memberId, 0));
                if (familyBasicInfo != null) {
                    memberInfo.setName(familyBasicInfo.getFamilyName());
                    memberInfo.setLogo(familyBasicInfo.getCover());
                    memberInfo.setHdLogo(familyBasicInfo.getCover());
                }
                break;
            default:
                throw new RuntimeException("role type not support,role type:" + roleType);

        }


        return memberInfo;
    }

    private MemberInfo getRoomInfo(String memberId) {
        int roomId = Convert.toInt(memberId, 0);
        RoomInfo roomInfo = commonService.getRoomInfoByRoomId(roomId);
        MemberInfo memberInfo = new MemberInfo();
        if (roomInfo != null) {
            memberInfo.setLogo(roomInfo.getCover());
            memberInfo.setName(roomInfo.getTitle());
            memberInfo.setHdLogo(roomInfo.getCover());
        }
        return memberInfo;
    }

    private void setAvatar(MemberInfo memberInfo, String roomMgrPic, String recommendPic) {
        if (StringUtils.isNotEmpty(roomMgrPic)) {
            memberInfo.setLogo(roomMgrPic);
            memberInfo.setHdLogo(roomMgrPic);
            return;
        }

        if (StringUtils.isNotEmpty(recommendPic)) {
            memberInfo.setLogo(recommendPic);
            memberInfo.setHdLogo(recommendPic);
        }
    }

    /**
     * 1 交友 2 约战 3 宝贝 4 打通房 101 派单房 201 游戏房 301 语音房 401 YY开黑房 501 技能卡房
     **/
    public static int changeToBusinessType(long busiId) {
        if (BusiId.MAKE_FRIEND.getValue() == busiId) {
            return 1;
        }
        if (BusiId.GAME_BABY.getValue() == busiId) {
            return 3;
        }
        if (BusiId.SKILL_CARD.getValue() == busiId) {
            return 501;
        }

        return 0;
    }

    private MemberInfo getUserInfo(Long uid) {
        MemberInfo memberInfo = new MemberInfo();
        UserBaseInfo userBaseInfo = commonService.getUserInfo(uid, false);
        if (userBaseInfo != null) {
            memberInfo.setLogo(userBaseInfo.getLogo());
            memberInfo.setName(userBaseInfo.getNick());
            memberInfo.setHdLogo(userBaseInfo.getHdLogo());
        }
        return memberInfo;
    }

    private MemberInfo getChannel(String memberId, RoleType roleType) {
        MemberInfo memberInfo = new MemberInfo();
        ChannelBaseInfo channelInfo = commonService.getChannelInfo(Convert.toLong(memberId, 0), false);
        if (channelInfo != null) {
            memberInfo.setAsid(channelInfo.getAsid() + "");
            String channelName = channelInfo.getName();
            memberInfo.setName(channelName);
            memberInfo.setLogo(channelInfo.getLogo() == null ? "" : channelInfo.getLogo());
            memberInfo.setHdLogo(memberInfo.getLogo());
        }

        return memberInfo;
    }

    public MemberInfo getSubChannel(String memberId, RoleType roleType) {
        MemberInfo memberInfo = new MemberInfo();
        String[] channelIdContent = memberId.split("_");
        final int two = 2;
        if (channelIdContent.length < two) {
            return memberInfo;
        }
        String sid = channelIdContent[0];
        String ssid = channelIdContent[1];
        ChannelBaseInfo channelInfo = commonService.getChannelInfo(Convert.toLong(sid, 0), false);
        if (channelInfo != null) {
            memberInfo.setAsid(channelInfo.getAsid() + "");
            String channelName = Convert.toString(channelInfo.getName());
            if (!sid.equals(ssid)) {
                Map<String, String> subChannelInfo = onlineSubChannelInfoService.getSubChannelInfo(memberId);
                channelName = subChannelInfo.getOrDefault("name", channelName);
            }
            memberInfo.setName(channelName);
            memberInfo.setLogo(channelInfo.getLogo() == null ? "" : channelInfo.getLogo());
        }
        memberInfo.setHdLogo(memberInfo.getLogo());
        return memberInfo;
    }

    public MemberInfo getPwChannel(String memberId) {
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setSid(memberId);

        MemberItemInfo memberItemInfo = peiwanThriftClient.queryChannel(memberId);
        if (memberItemInfo != null) {
            memberInfo.setName(memberItemInfo.getBaseFieldMemberName());
            memberInfo.setLogo(memberItemInfo.getBaseFieldMemberUrl());
            if (memberItemInfo.getExt() != null) {
                memberInfo.setAsid(memberItemInfo.getExt().getOrDefault("asid", ""));
            }
        }

        return memberInfo;
    }

    public MemberInfo getPwTeam(String memberId) {
        MemberInfo memberInfo = new MemberInfo();
        MemberItemInfo memberItemInfo = peiwanThriftClient.queryTeam(memberId);
        if (memberItemInfo != null) {
            memberInfo.setName(memberItemInfo.getBaseFieldMemberName());
            memberInfo.setAsid(memberItemInfo.getExt().getOrDefault("asid", ""));
            memberInfo.setSid(memberItemInfo.getExt().getOrDefault("sid", ""));
        }
        return memberInfo;
    }

    public Map<String, MemberInfo> getPwTeamMap(List<String> memberIds) {
        Map<String, MemberInfo> result = Maps.newHashMap();
        Map<String, MemberItemInfo> memberItemInfoMap = peiwanThriftClient.queryTeams(memberIds);
        for (String member : memberItemInfoMap.keySet()) {
            MemberInfo memberInfo = new MemberInfo();

            MemberItemInfo memberItemInfo = memberItemInfoMap.get(member);
            memberInfo.setTeamId(member);
            memberInfo.setName(memberItemInfo.getBaseFieldMemberName());
            String sid = memberItemInfo.getExt().getOrDefault("sid", "");
            memberInfo.setAsid(memberItemInfo.getExt().getOrDefault("asid", sid));
            if(StringUtil.isEmpty(memberInfo.getAsid())){
                memberInfo.setAsid(sid);
            }
            memberInfo.setSid(sid);
            result.put(member, memberInfo);
        }
        return result;
    }


    public Map<String, MemberItemInfo> getBatchQueryChannel(List<String> chIds) {
        if (CollectionUtils.isEmpty(chIds)) {
            return null;
        }
        return peiwanThriftClient.batchQueryChannel(chIds);
    }

    public Map<String, MemberItemInfo> getBatchQueryPwInfo(List<String> ppUids) {
        if (CollectionUtils.isEmpty(ppUids)) {
            return null;
        }
        return peiwanThriftClient.batchQueryPwInfo(ppUids);
    }

    public @NotNull Map<String, MemberInfo> querySubChannelMemberInfo(List<String> memberIds) {
        Map<String, WebdbSubChannelInfo> subChannels = webdbServiceClient.batchGetSubChannelInfo(memberIds);
        List<Long> sids = Lists.newArrayList();
        memberIds.forEach(x -> {
            String[] idArrar = x.split("_");
            sids.add(Convert.toLong(idArrar[0], 0));
        });
        Map<Long, WebdbChannelInfo> channelInfoMap = webdbThriftClient.batchGetChannelInfo(sids);


        Map<String, MemberInfo> memberInfoMap = Maps.newHashMap();
        for (String memberId : memberIds) {
            MemberInfo memberInfo = new MemberInfo();

            WebdbSubChannelInfo subChannelInfo = subChannels.get(memberId);
            if (subChannelInfo != null) {
                memberInfo.setName(subChannelInfo.getName());
            }

            String[] split = memberId.split("_");
            WebdbChannelInfo channelInfo = channelInfoMap.get(Convert.toLong(split[0], 0));
            if (channelInfo != null) {
                memberInfo.setLogo(WebdbUtils.getLogo(channelInfo));
                memberInfo.setAsid(channelInfo.getAsid());
                if (StringUtil.isBlank(memberInfo.getName())) {
                    memberInfo.setName(channelInfo.getName());
                }
            }
            memberInfoMap.put(memberId, memberInfo);
        }
        return memberInfoMap;
    }

}
