package com.yy.gameecology.activity.client.yrpc;

import com.google.common.collect.Lists;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.protocol.pb.currency.CurrencyProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class CurrencyClient {
    @Reference(protocol = "yrpc", owner = "${currency.s2s}", registry = {"yrpc-reg"}, lazy = true)
    private CurrencyProvider proxy;

    @Reference(protocol = "yrpc", owner = "${currency.s2s}", registry = {"yrpc-reg"}, lazy = true,retries = 2, cluster = "failover")
    private CurrencyProvider readProxy;

    public CurrencyProvider getProxy() {
        return proxy;
    }

    public CurrencyProvider getReadProxy() {
        return readProxy;
    }

    /*
        SUCCESS(1, "SUCCESS"),
        TOO_FAST(2, "TOO FAST"),
        NOT_ENOUGH(3, "NOT ENOUGH"),
        PARAMETER_ERROR(4, "PARAMETER ERROR"),
        SYSTEM_ERROR(500, "系统异常")
    */

    /**
     * 查询余额， 返回 null 表示发生错误
     *
     * @param uid
     * @param busiId
     * @param cids
     * @return
     */
    public Map<String, Long> balance(long uid, int busiId, List<String> cids) {
        Map<String, Long> map = new HashMap<>();
        if (cids == null || cids.isEmpty()) {
            return map;
        }
        CurrencyProto.GetMultiCurrencyRequest getMultiCurrencyRequest
                = CurrencyProto.GetMultiCurrencyRequest.newBuilder().setUid(uid).setBusiId(busiId).addAllMetaIds(cids).build();
        try {
            CurrencyProto.GetMultiCurrencyResult ret = readProxy.getMultiCurrency(getMultiCurrencyRequest);
            if (ret.getCode() == 1 && !ret.getCurrencysList().isEmpty()) {
                for (CurrencyProto.Currency currency : ret.getCurrencysList()) {
                    map.put(currency.getMetaId(), currency.getAmount());
                }
                return map;
            }
            log.warn("currency warn ret:{}", JsonFormat.printToString(ret));
            return map;
        } catch (Exception e) {
            log.error("currency ser getMultiCurrency uid:{}, e:{}", uid, ExceptionUtils.getStackTrace(e));
            return null;
        }
    }

    public long balance(long uid, int busiId, String cid) {
        Map<String, Long> balance = balance(uid, busiId, Lists.newArrayList(cid));
        return balance.getOrDefault(cid, 0L);
    }

    /**
     * 批量扣款
     *
     * @param uid
     * @param busiId
     * @param cidCosts - 货币花费， key：货币id， key：花费数量
     * @param seq
     * @return
     */
    public int consume(long uid, int busiId, Map<String, Long> cidCosts, String seq) {
        List<CurrencyProto.Currency> currencies = new ArrayList<>();
        for (String cid : cidCosts.keySet()) {
            long amount = cidCosts.get(cid);
            currencies.add(CurrencyProto.Currency.newBuilder()
                    .setMetaId(cid).setAmount(amount).setBusiId(busiId).build());
        }
        CurrencyProto.ConsumeProductRequest consumeProductRequest
                = CurrencyProto.ConsumeProductRequest.newBuilder()
                .setUid(uid).setBusiId(busiId).addAllCurrencys(currencies)
                .setSeqId(seq).build();
        try {
            CurrencyProto.ConsumeProductResult ret = proxy.consumeProduct(consumeProductRequest);
            log.info("currency ser consume, seq:{}, ret:{}", seq, ret.getCode());
            //请求过快 retry
            if (ret.getCode() == 2) {
                log.info("currency ser consume, retry, seq:{}, uid:{}", seq, uid);
                int retry = 3;
                while (retry-- > 0) {
                    ret = proxy.consumeProduct(consumeProductRequest);
                    if (ret.getCode() != 2) {
                        return ret.getCode();
                    }
                    if (retry > 0) {
                        SysEvHelper.waiting(300);
                    }
                }
            }
            return ret.getCode();
        } catch (Exception e) {
            log.error("currency ser consume uid:{}, seq:{}, e:{}", uid, seq, ExceptionUtils.getStackTrace(e));
        }
        return 500;
    }

    public boolean issue(long uid, long activityId, String cid, long amount, Date endTime, int busiId, String seq) {
        List<CurrencyProto.Currency> currencies = new ArrayList<>();
        CurrencyProto.Currency currency = CurrencyProto.Currency.newBuilder()
                .setMetaId(cid).setAmount(amount).setBusiId(busiId).build();
        currencies.add(currency);
        CurrencyProto.IssueMultiCurrencyRequest issueMultiCurrencyRequest
                = CurrencyProto.IssueMultiCurrencyRequest.newBuilder()
                .setUid(uid).setActivityId(activityId).addAllCurrencys(currencies)
                .setEffectiveEndTime(endTime.getTime()).setBusiId(busiId).setSeqId(seq)
                .build();
        try {
            CurrencyProto.IssueMultiCurrencyResult ret = proxy.issueCurrency(issueMultiCurrencyRequest);
            if (ret.getCode() == 1) {
                return true;
            }
            log.warn("currency ser issue error, seq:{}, uid:{}, ret:{}", seq, uid, ret.getCode());
        } catch (Exception e) {
            log.error("currency ser issue, seq:{}, uid:{}, e:{}", seq, uid, ExceptionUtils.getStackTrace(e));
        }
        return false;
    }

    public boolean rollback(long uid, String seq, int busiId) {
        CurrencyProto.RollbackRequest rollbackRequest =
                CurrencyProto.RollbackRequest.newBuilder().setBusiId(busiId).setSeqId(seq).build();
        try {
            CurrencyProto.ConsumeProductResult ret = proxy.rollback(rollbackRequest);
            log.info("currency ser rollback, uid:{}, seq:{}, ret:{}", uid, seq, ret.getCode());
            //请求过快 retry
            if(ret.getCode() != 1) {
                log.warn("currency ser rollback error, uid:{}, seq:{}, busiId:{}, ret:{}", uid, seq, busiId, JsonFormat.printToString(ret));
                return false;
            }
            return true;
        }catch (Exception e) {
            log.error("currency ser rollback error, uid:{}, seq:{}, busiId:{}, error:{}", uid, seq, busiId, ExceptionUtils.getStackTrace(e));
        }
        return false;
    }

    public List<CurrencyProto.UserCurrency> getUserCurrencyStat(int busiId, String metaId, int size) {
        CurrencyProto.CurrencyStatRequest req = CurrencyProto.CurrencyStatRequest.newBuilder()
                .setBusiId(busiId)
                .setMetaId(metaId)
                .setAccountType(CurrencyProto.AccountType.FOREVER)
                .setSize(size)
                .build();

        try {
            CurrencyProto.CurrencyStatResponse resp = proxy.getCurrencyStat(req);
            if (resp != null && resp.getCode() == 1) {
                return resp.getListList();
            }
        } catch (Exception e) {
            log.error("getUserCurrencyStat fail:", e);
        }

        return Collections.emptyList();
    }
}
