package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.acttask.MemberCurTaskVo;
import com.yy.gameecology.activity.bean.acttask.MemberTaskItemVo;
import com.yy.gameecology.activity.bean.acttask.MemberTaskVo;
import com.yy.gameecology.activity.bean.hdzt.RankItem;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.rank.UserRankItem;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.model.gameecology.*;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-10-22 20:33
 **/
@Service
public class ActTaskService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    //成员完成任务记录 hash : 任务配置id_level
    private static final String TASK_COMPLETE_RECORD_KEY = "task:complete_record:%s_%s";

    //成员贡献榜 set : 任务配置id:成员id
    private static final String TASK_COMPLETE_SRC_KEY = "task:complete_src:%s:%s";

    @Autowired
    private CommonService commonService;

    @Autowired
    private GameecologyDao gameecologyDao;

    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private RedisConfigManager redisConfigManager;


    @Autowired
    private CacheService cacheService;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private EnrollmentService enrollmentService;

    /**
     * 处理高光时刻任务
     */
    public void handlerTaskEvent(RankingScoreChanged event) {
        if (SysEvHelper.isDev()) {
            log.info("handlerTaskEvent,event:{}", JSON.toJSONString(event));
        }

        String groupCode = redisConfigManager.getGroupCode(event.getActId());
        //seq 去重
        String duplicateKey = Const.addActivityPrefix(event.getActId(), "handlerTaskEvent:" + event.getSeq());
        if (!actRedisDao.setNX(groupCode, duplicateKey, System.currentTimeMillis() + "", DateUtil.ONE_WEEK_SECONDS)) {
            log.info("{} has process return", duplicateKey);
            return;
        }

        Date now = commonService.getNow(event.getActId());

        long actId = event.getActId();
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        long busiId = event.getBusiId();

        String member = event.getMember();

        long addScore = event.getItemScore();


        //检查榜单有无配置任务
        List<ActTask> taskConfigs = cacheService.getActTaskConfigs(actId, rankId, phaseId);
        if (CollectionUtils.isEmpty(taskConfigs)) {
            return;
        }
        for (ActTask taskConfig : taskConfigs) {
            long taskId = taskConfig.getTaskId();

            //检查当前成员有无任务设置
            ActTaskMember actMemberTask = cacheService.getActTaskMember(actId, taskId, member);
            if (actMemberTask == null) {
                continue;
            }

            //不在任务时间内不处理、非有效任务不处理
            if (now.getTime() < taskConfig.getStartTime().getTime()
                    || now.getTime() > taskConfig.getEndTime().getTime()
                    || taskConfig.getStatus() <= 0) {
                continue;
            }

            //任务累分 prefix.task:taskId
            String key = makeTaskScoreKey(actId, taskId);
            long curScore = actRedisDao.getRedisTemplate(groupCode).opsForZSet().incrementScore(key, member, addScore).longValue();

            boolean isCompleteTask = false;
            //看有无完成任务条件
            //TODO 正赛#支持过多梯度任务
            Map<Integer, List<ActTaskItem>> actTaskItemMap = cacheService.getActTaskItem(taskId);
            Integer completeLevel = 0;
            for (Integer level : actTaskItemMap.keySet()) {
                List<ActTaskItem> actTaskItems = actTaskItemMap.get(level);
                isCompleteTask = isCompleteTask(member, curScore, actTaskItems);
                //TODO
                completeLevel = level;
            }

            if (!isCompleteTask) {
                //记录成员贡献榜(任务完成后不再累榜，锁定贡献榜)
                saveContributeRank(event, taskConfig, member, addScore);
            } else {

                //TODO 正赛#支持过多梯度任务,还要记录当前完成的level
                String taskCompleteKey = Const.addActivityPrefix(actId, String.format(TASK_COMPLETE_RECORD_KEY, taskId, completeLevel));
                boolean firstInvoke = actRedisDao.getRedisTemplate(groupCode).opsForHash().putIfAbsent(taskCompleteKey, member, DateUtil.format(now));

                if (!firstInvoke) {
                    continue;
                }
                //完成任务
                handleTaskComplete(event, taskConfig, curScore);
            }
        }
    }


    //查询用户任务
    public List<MemberTaskVo> queryActMemberTask(Long actId, String memberIdsStr, Integer roleType, Integer taskStatus) {
        List<MemberTaskVo> memberTaskVos = Lists.newArrayList();

        Date now = commonService.getNow(actId);
        String memberId = memberIdsStr;
        List<ActTaskMember> actTaskMembers = cacheService.getActTaskMembers(actId, memberId);
        if (CollectionUtils.isEmpty(actTaskMembers)) {
            return memberTaskVos;
        }

        List<ActTask> actTasks = Lists.newArrayList();
        for (ActTaskMember actTaskMember : actTaskMembers) {
            ActTask actTask = cacheService.getActTaskConfig(actId, actTaskMember.getTaskId(), taskStatus);
            if (actTask != null) {
                actTasks.add(actTask);
            }
        }

        //任务序号从小到大排 TODO test
        actTasks = actTasks.stream().sorted(Comparator.comparing(ActTask::getSort)).collect(Collectors.toList());


        for (ActTask actTask : actTasks) {
            if (actTask == null) {
                continue;
            }

            //---任务基本信息
            MemberTaskVo vo = new MemberTaskVo();
            vo.setCurTime(now.getTime());
            vo.setStartTime(actTask.getStartTime().getTime());
            vo.setTaskId(actTask.getTaskId());
            vo.setMemberId(memberIdsStr);
            vo.setTitle(actTask.getTitle());
            MemberInfo memberInfo = memberInfoService.getMemberInfo(0L, RoleType.findByValue(roleType), memberIdsStr);
            if (memberInfo != null) {
                vo.setName(memberInfo.getName());
            }
            EnrollmentInfo enrollmentInfo = enrollmentService.getFirstEnrolMember(actId, 0L, roleType, memberId);
            if (enrollmentInfo != null) {
                vo.setAsid(enrollmentInfo.getSignAsid() + "");
            }

            vo.setAwardText(actTask.getAwardText());
            vo.setRoleType(actTask.getRoleType());

            Date endTime = actTask.getEndTime();
            long leftSeconds = (endTime.getTime() - now.getTime()) / 1000;
            if (leftSeconds < 0) {
                leftSeconds = 0;
            }
            vo.setLeftSeconds(leftSeconds);
            vo.setSort(actTask.getSort());

            //子任务信息
            List<MemberTaskItemVo> taskItemVos = Lists.newArrayList();
            Map<Integer, List<ActTaskItem>> itemsMap = cacheService.getActTaskItem(actTask.getTaskId());
            for (Integer level : itemsMap.keySet()) {
                List<ActTaskItem> actTaskItems = itemsMap.get(level);
                actTaskItems.forEach(x -> {
                    MemberTaskItemVo taskItemVo = new MemberTaskItemVo();
                    taskItemVo.setName(x.getItemName());
                    taskItemVo.setType(x.getType());
                    taskItemVo.setDesc(x.getDesc());
                    taskItemVo.setLevel(x.getLevel());
                    JSONObject passPolicyObject = JSON.parseObject(x.getPassPolicy());
                    long passScore = passPolicyObject.getLong("passScore");
                    taskItemVo.setTaskScore(passScore);
                    long curScore = getTaskCurScore(actId, actTask.getTaskId(), memberId);
                    taskItemVo.setCurScore(curScore);
                    int state = getTaskItemState(actId, actTask.getTaskId(), level, memberId);
                    taskItemVo.setState(state);

                    taskItemVos.add(taskItemVo);
                });

            }
            vo.setTaskItems(taskItemVos);

            if (actTask.getAwardRankCount() > 0) {
                String groupCode = redisConfigManager.getGroupCode(actId);
                List<UserRankItem> rankItems = queryContributeRank(groupCode, memberId, actTask);
                vo.setRanks(rankItems);
            }

            memberTaskVos.add(vo);
        }

        return memberTaskVos;
    }

    /**
     * 查询成员当前正在过的任务
     * 有多个任务的时候只展示其中1个
     */
    public MemberCurTaskVo queryMemberCurTask(long actId, RoleType roleType, String memberId) {
        //TODO 正赛#有梯度的时候如何展示？

        Date now = commonService.getNow(actId);
        long nowTime = now.getTime();

        List<ActTaskMember> actTaskMembers = cacheService.getActTaskMembers(actId, memberId);
        if (CollectionUtils.isEmpty(actTaskMembers)) {
            return null;
        }

        List<ActTask> actTasks = Lists.newArrayList();
        for (ActTaskMember actTaskMember : actTaskMembers) {
            ActTask actTask = cacheService.getActTaskConfig(actId, actTaskMember.getTaskId(), 1);
            if (actTask != null) {
                actTasks.add(actTask);
            }
        }

        //任务序号从小到大排 TODO test
        actTasks = actTasks.stream().sorted(Comparator.comparing(ActTask::getSort)).collect(Collectors.toList());

        //优先展示未完成的，前面的任务,匹配到没有才展示最后的任务
        MemberCurTaskVo lastTask = null;

        for (ActTask actTask : actTasks) {
            if (actTask == null) {
                continue;
            }
            if (nowTime < actTask.getStartTime().getTime() || nowTime > actTask.getEndTime().getTime()) {
                continue;
            }
            MemberCurTaskVo vo = new MemberCurTaskVo();
            vo.setTaskId(actTask.getTaskId());
            vo.setTaskName(actTask.getTitle());
            vo.setMemberId(memberId);
            long passScore = getPassScore(memberId, actTask);
            vo.setTaskScore(passScore);
            long curScore = getTaskCurScore(actId, actTask.getTaskId(), memberId);
            vo.setCurScore(curScore);
            MemberInfo memberInfo = memberInfoService.getMemberInfo(0L, roleType, memberId);
            if (memberInfo != null) {
                vo.setMemberName(Base64.encodeBase64String(Convert.toString(memberInfo.getName()).getBytes()));
                vo.setAvatarInfo(memberInfo.getLogo());
            }
            if (curScore < passScore) {
                return vo;
            }

            lastTask = vo;
        }

        return lastTask;
    }

    //---任务完成逻辑: 记录任务完成状态、奖励发放、记录锁定的贡献榜单、单播广播特效
    private void handleTaskComplete(RankingScoreChanged event, ActTask taskConfig, long curScore) {


        long actId = event.getActId();
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        long busiId = event.getBusiId();
        String groupCode = redisConfigManager.getGroupCode(actId);

        String member = event.getMember();

        long addScore = event.getItemScore();
        long taskId = taskConfig.getTaskId();
        int roleType = taskConfig.getRoleType();

        //记录完成任务数据
        ActTaskAwardRecord record = new ActTaskAwardRecord();
        record.setCtime(new Date());
        record.setActId(event.getActId());
        record.setSeq(event.getSeq());
        record.setTaskId(taskId);
        record.setPackageId(0L);
        record.setPackageName(taskConfig.getTitle());
        record.setPackageNum(0L);
        record.setMemberId(member);
        record.setStatus(1L);
        record.setPlatform(0L);
        record.setAddress("任务类型" + roleType);
        record.setCtime(new Date());
        record.setUtime(new Date());
        gameecologyDao.insert(ActTaskAwardRecord.class, record);

        //记录成员贡献榜(任务完成后不再累榜，锁定贡献榜)
        saveContributeRank(event, taskConfig, member, addScore);

        log.info("task complete event:{}", JSON.toJSONString(event));
        log.info("task complete,taskConfigId:{},actId:{},rankId:{},phaseId:{},score:{},member:{}"
                , taskId, actId, rankId, phaseId, curScore, member);

        List<RankItem> contributeRankItem = getContributeRankBaseInfo(groupCode, member, taskConfig);
        contributeRankItem.forEach(x -> {
            ActTaskAwardRecord recordContribute = new ActTaskAwardRecord();
            recordContribute.setSeq(event.getSeq());
            recordContribute.setCtime(new Date());
            recordContribute.setActId(event.getActId());
            recordContribute.setTaskId(taskId);
            recordContribute.setPackageId(0L);
            recordContribute.setPackageName(taskConfig.getTitle());
            recordContribute.setPackageNum(0L);
            recordContribute.setMemberId(x.getMember());
            recordContribute.setStatus(1L);
            recordContribute.setPlatform(0L);
            recordContribute.setAddress("贡献榜-任务类型" + roleType);
            recordContribute.setCtime(new Date());
            recordContribute.setUtime(new Date());
            gameecologyDao.insert(ActTaskAwardRecord.class, recordContribute);
        });


        //给完成任务的成员发奖奖励
        List<ActTaskAward> awards = cacheService.getTaskAward(taskId, roleType);
        log.info("begin task award member,taskConfigId:{},actId:{},rankId:{},phaseId:{},score:{},member:{},awards:{}",
                taskId, actId, rankId, phaseId, curScore, member, JSON.toJSONString(awards));
        awards.forEach(x -> {
            //TODO 失败重试
            String seq = UUID.randomUUID().toString();
            long memberId = Convert.toLong(member, 0);
            hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), memberId, busiId, x.getHdztTaskId(), x.getAmount(), x.getHdztPackageId(), seq);
        });


        //给贡献的成员发放奖励
        int contributeRoleType = -1;
        if (roleType == RoleType.GUILD.getValue()) {
            contributeRoleType = RoleType.ANCHOR.getValue();
        } else if (roleType == RoleType.ANCHOR.getValue()) {
            contributeRoleType = RoleType.USER.getValue();
        }
        for (RankItem contribute : contributeRankItem) {
            List<ActTaskAward> contributeAwards = cacheService.getTaskAward(taskId, contributeRoleType);
            contributeAwards.forEach(x -> {
                //TODO 失败重试
                String seq = UUID.randomUUID().toString();
                long memberId = Convert.toLong(contribute.getMember(), 0);
                hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), memberId, busiId, x.getHdztTaskId(), x.getAmount(), x.getHdztPackageId(), seq);
            });
        }


        MemberInfo memberInfo = memberInfoService.getMemberInfo(0L, RoleType.findByValue(roleType), member);

        String memberNameBase64 = memberInfo == null || memberInfo.getName() == null ? "" : Base64.encodeBase64String(memberInfo.getName().getBytes());

        //主播任务 完成任务通知主播
        if (roleType == RoleType.ANCHOR.getValue()) {
            log.info("popExclusiveMoment,actId:{},taskId:{},uid:{},memberInfo:{}", actId, taskId, JSON.toJSONString(memberInfo));
            Long uid = Convert.toLong(member, 0);
            commonBroadCastService.popExclusiveMoment(Collections.singletonList(uid)
                    , actId, memberNameBase64, Convert.toLong(memberInfo.getAsid(), 0), Lists.newArrayList(), "【高光时刻】", 1);
        }

        //公会或者厅任务通知贡献主播
        if (roleType == RoleType.GUILD.getValue() || roleType == RoleType.HALL.getValue()) {
            contributeRankItem.forEach(x -> {
                log.info("popExclusiveMoment,actId:{},taskId:{},uid:{},memberInfo:{},anchor:{}", actId, taskId, JSON.toJSONString(memberInfo), JSON.toJSONString(x));
                Long uid = Convert.toLong(x.getMember(), 0);
                MemberInfo memberAnchorInfo = memberInfoService.getMemberInfo(0L, RoleType.ANCHOR, x.getMember());
                String memberAnchorNickBase64 = memberAnchorInfo == null || memberAnchorInfo.getName() == null ? "" : Base64.encodeBase64String(memberAnchorInfo.getName().getBytes());
                commonBroadCastService.popExclusiveMoment(Collections.singletonList(uid)
                        , actId, memberAnchorNickBase64, Convert.toLong(memberInfo.getAsid(), 0), Lists.newArrayList(), "【高光时刻】", 2);
            });
        }

        // 广播特效
        Template template = null;
        if (busiId == BusiId.MAKE_FRIEND.getValue()) {
            template = Template.Jiaoyou;
        } else if (busiId == BusiId.GAME_BABY.getValue()) {
            template = Template.Gamebaby;
        } else if (busiId == BusiId.YUE_ZHAN.getValue()) {
            template = Template.Yuezhan;
        }
        if (template != null) {
            log.info("sendHighlightMomentMotion roleType:{},nick:{},asid:{},curScore:{},template:{}",
                    roleType, memberInfo.getName(), memberInfo.getAsid(), curScore, template);
            DecimalFormat df = new DecimalFormat();
            df.setMaximumFractionDigits(2);
            String scoreW = curScore < 10000 ? df.format(curScore / 10000f) : (curScore / 10000) + "";
            commonBroadCastService.sendHighlightMomentMotion(roleType, memberNameBase64, Convert.toLong(memberInfo.getAsid(), 0), scoreW, template);
        } else {
            log.error("sendHighlightMomentMotion error, not found template,event:{}", JSON.toJSONString(event));
        }
    }


    private void saveContributeRank(RankingScoreChanged event, ActTask taskConfig, String member, long addScore) {
        long actId = event.getActId();
        long taskId = taskConfig.getTaskId();
        String groupCode = redisConfigManager.getGroupCode(actId);
        //记录成员贡献榜(任务完成后不再累榜，锁定贡献榜)
        String contributeMemberId = findContributeMember(event, taskConfig);
        if (StringUtil.isNotBlank(contributeMemberId)) {
            String contributeRankKey = makeContributeRankKey(actId, taskId, member);
            actRedisDao.getRedisTemplate(groupCode).opsForZSet().incrementScore(contributeRankKey, contributeMemberId, addScore);
        }
    }


    //获取用户当前要完成任务的分数
    private long getPassScore(String memberId, ActTask actTask) {
        //TODO 正赛#阶梯任务的时候需要读取用户当前等级
        Map<Integer, List<ActTaskItem>> actTaskItemsMap = cacheService.getActTaskItem(actTask.getTaskId());
        for (Integer level : actTaskItemsMap.keySet()) {
            List<ActTaskItem> actTaskItems = actTaskItemsMap.get(level);
            for (ActTaskItem item : actTaskItems) {
                //过关类型 1-榜单分值 2-赛程榜单排名
                if (item.getType() == 1) {
                    String policy = item.getPassPolicy();
                    JSONObject passPolicyObject = JSON.parseObject(policy);
                    long passScore = passPolicyObject.getLong("passScore");
                    return passScore;
                }
            }
        }

        return -1;
    }

    //当前任务是否完成
    private int getTaskItemState(long actId, long taskId, int completeLevel, String member) {
        //TODO 正赛#支持过多梯度任务,还要记录当前完成的level
        String groupCode = redisConfigManager.getGroupCode(actId);
        String taskCompleteKey = Const.addActivityPrefix(actId, String.format(TASK_COMPLETE_RECORD_KEY, taskId, completeLevel));
        Object task = actRedisDao.getRedisTemplate(groupCode).opsForHash().get(taskCompleteKey, member);
        return task != null ? 1 : 0;
    }

    //获取贡献成员id
    private String findContributeMember(RankingScoreChanged event, ActTask taskConfig) {
        String contributeRoleId = taskConfig.getAwardRole();
        if (StringUtil.isBlank(contributeRoleId)) {
            return "";
        }
        Map<Long, String> actors = event.getActors();
        for (Long roleId : actors.keySet()) {
            if (contributeRoleId.contains(roleId + "")) {
                return actors.get(roleId);
            }
        }

        return "";
    }

    private String makeContributeRankKey(long actId, long taskId, String member) {
        return Const.addActivityPrefix(actId, String.format(TASK_COMPLETE_SRC_KEY, taskId, member));
    }

    private String makeTaskScoreKey(long actId, long taskId) {
        return Const.addActivityPrefix(actId, "task:" + taskId);
    }

    private boolean isCompleteTask(String memberId, long curScore, List<ActTaskItem> taskItems) {
        //全部完成了才算完成

        for (ActTaskItem taskItem : taskItems) {
            int type = taskItem.getType();
            String passPolicy = taskItem.getPassPolicy();
            JSONObject passPolicyObject = JSON.parseObject(passPolicy);
            //过关类型 1-榜单分值
            if (type == 1) {
                long passScore = passPolicyObject.getLong("passScore");
                if (curScore < passScore) {
                    return false;
                }

            }
            //2-榜单排名
            else if (type == 2) {
                //TODO 正赛#如果是排名类型的任务，应该是接收到中台结算事件的时候看排名，万一运营要自己确定结算时间点则要通过定时器
                return false;

            } else {
                log.error("ActTaskItem config type error,taskId:{}", taskItem.getTaskId());
            }

        }

        return true;
    }


    /**
     * 任务用户完成的分数
     */
    private long getTaskCurScore(Long actId, Long taskId, String member) {
        String key = makeTaskScoreKey(actId, taskId);
        String groupCode = redisConfigManager.getGroupCode(actId);
        Double score = actRedisDao.getRedisTemplate(groupCode).opsForZSet().score(key, member);
        return score == null ? 0 : score.longValue();
    }

    //查询榜单成员贡献榜(主播、神豪)
    private List<UserRankItem> queryContributeRank(String groupCode, String memberId, ActTask config) {
        List<UserRankItem> rankItems = Lists.newArrayList();

        List<RankItem> baseInfo = getContributeRankBaseInfo(groupCode, memberId, config);
        List<Long> uids = baseInfo.stream().map(x -> Convert.toLong(x.getMember())).collect(Collectors.toList());
        Map<Long, WebdbUserInfo> userInfoMap = commonService.batchYyUserInfo(uids);
        baseInfo.forEach(x -> {
            UserRankItem userRankItem = new UserRankItem();
            Long uid = Convert.toLong(x.getMember(), 0);
            userRankItem.setUid(uid);
            userRankItem.setValue(x.getScore());
            WebdbUserInfo userInfo = userInfoMap.get(uid);
            String nick = userInfo == null ? "神秘主播" : userInfo.getNick();
            String avatar = WebdbUtils.getLogo(userInfo);
            userRankItem.setNick(nick);
            userRankItem.setAvatarInfo(avatar);
            rankItems.add(userRankItem);
        });

        return rankItems;
    }

    //查询榜单成员贡献基础信息
    private List<RankItem> getContributeRankBaseInfo(String groupCode, String memberId, ActTask config) {
        List<RankItem> rankItems = Lists.newArrayList();

        String key = makeContributeRankKey(config.getActId(), config.getTaskId(), memberId);
        Set<ZSetOperations.TypedTuple<String>> typedTuple = actRedisDao.getRedisTemplate(groupCode).opsForZSet().reverseRangeWithScores(key, 0, config.getAwardRankCount() - 1);
        int rank = 1;
        for (ZSetOperations.TypedTuple<String> item : typedTuple) {
            RankItem rankItem = new RankItem();
            rankItem.setScore(item.getScore().longValue());
            rankItem.setMember(item.getValue());
            rankItem.setRank(rank);
            rankItems.add(rankItem);

            rank++;
        }

        return rankItems;
    }


}
