package com.yy.gameecology.activity.client.yrpc;

import com.google.common.cache.CacheBuilder;
import com.yy.gameecology.activity.bean.grade.GradeIcon;
import com.yy.protocol.pb.skillcard.grade.CommonGrade;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

@Component
public class CommonGradeClient {

    private static final ConcurrentMap<Long, GradeIcon> GRADE_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(3, TimeUnit.MINUTES)
            .maximumSize(2000)
            .<Long, GradeIcon>build()
            .asMap();

    @Reference(protocol = "yrpc", owner = "skillcard_server_yrpc", registry = "yrpc-reg")
    private CommonGradeProvider proxy;

    public Map<Long, GradeIcon> batchGetCommonGrade(Collection<Long> uids) {
        if (CollectionUtils.isEmpty(uids)) {
            return Collections.emptyMap();
        }

        Map<Long, GradeIcon> result = new HashMap<>(uids.size());
        Set<Long> queryUids = new HashSet<>(uids.size());
        for (Long uid : uids) {
            GradeIcon g = GRADE_CACHE.get(uid);
            if (g == null) {
                queryUids.add(uid);
                continue;
            }

            result.put(uid, g);
        }

        if (queryUids.isEmpty()) {
            return result;
        }

        var gradeIcons = batchGetCommonGradeIcon(queryUids);
        if (CollectionUtils.isEmpty(gradeIcons)) {
            return result;
        }

        for (var gradeIcon : gradeIcons) {
            var item = parseGradeIcon(gradeIcon);
            result.put(gradeIcon.getUid(), item);
            GRADE_CACHE.put(gradeIcon.getUid(), item);
        }

        return result;
    }

    private List<CommonGrade.CommonGradeIcon> batchGetCommonGradeIcon(Collection<Long> uids) {
        if (CollectionUtils.isEmpty(uids)) {
            return Collections.emptyList();
        }

        CommonGrade.QueryIconReq req = CommonGrade.QueryIconReq.newBuilder()
                .addAllUid(uids)
                .setType(1)
                .build();

        var rsp = proxy.queryIcon(req);
        if (rsp == null || rsp.getCode() != 0) {
            return Collections.emptyList();
        }

        return rsp.getIconList();
    }

    private GradeIcon parseGradeIcon(CommonGrade.CommonGradeIcon gradeIcon) {
        GradeIcon result = new GradeIcon();
        result.setIcon(gradeIcon.getIcon());
        result.setIconLong(gradeIcon.getIconLong());
        result.setIconBig(gradeIcon.getIconBig());
        result.setLevel(gradeIcon.getLevel());
        result.setIconLongDynamic(gradeIcon.getIconLongDynamic());
        return result;
    }
}
