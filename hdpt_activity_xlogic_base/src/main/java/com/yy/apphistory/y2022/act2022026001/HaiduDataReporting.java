package com.yy.apphistory.y2022.act2022026001;

import com.yy.gameecology.activity.service.BigDataService;
import com.yy.gameecology.common.consts.BigDataScoreType;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 22年交友积分赛海度数据上报
 *
 * <AUTHOR>
 * @date 2022/3/1 9:55
 **/
@Component
public class HaiduDataReporting {
    @Autowired
    private BigDataService bigDataService;

    /**
     * 上报周星积分/周星
     *
     * @param actId          活动id
     * @param member         主播id
     * @param score          积分
     * @param rank           周星的榜单排名
     * @param actorId        50001 主持 50014 天团
     * @param threshold   达到的门槛
     **/
    public void reportWeekStarScore(long actId, String member, long score, int rank, long actorId, long threshold, int type) {
        bigDataService.saveNoRankDataToFile(actId, BusiId.MAKE_FRIEND, System.currentTimeMillis(), member, RoleType.ANCHOR, score
                , type == 1 ? BigDataScoreType.WEEK_STAR_POINT : BigDataScoreType.WEEK_STAR_DEBRIS, threshold + "", rank, actorId);
    }



    /**
     * 上报乱斗积分
     *
     * @param actId   活动id
     * @param member  主播id
     * @param score   积分
     * @param actorId 50001 主持 50014 天团
     **/
    public void reportLDScore(long actId, String member, long score, long actorId) {
        bigDataService.saveNoRankDataToFile(actId, BusiId.MAKE_FRIEND, System.currentTimeMillis(), member, RoleType.ANCHOR, score
                , BigDataScoreType.LD_POINT, "", 0, actorId);
    }

    /**
     * 上报碎片兑换积分
     *
     * @param actId   活动id
     * @param member  主播id
     * @param score   积分
     * @param actorId 50001 主持 50014 天团
     **/
    public void reportDebrisExchangeScore(long actId, String member, long score, long actorId) {
        bigDataService.saveNoRankDataToFile(actId, BusiId.MAKE_FRIEND, System.currentTimeMillis(), member, RoleType.ANCHOR, score
                , BigDataScoreType.DEBRIS_EXCHANGE_POINT, "", 0, actorId);
    }

    /**
     * 上报收集礼物得碎片
     *
     * @param actId   活动id
     * @param member  主播id
     * @param score   积分
     * @param level   收集到的礼物数量
     * @param actorId 50001 主持 50014 天团
     **/
    public void reportDebris(long actId, String member, long score, int level, long actorId) {
        bigDataService.saveNoRankDataToFile(actId, BusiId.MAKE_FRIEND, System.currentTimeMillis(), member, RoleType.ANCHOR, score
                , BigDataScoreType.COLLECT_DEBRIS, "", level, actorId);
    }
}
